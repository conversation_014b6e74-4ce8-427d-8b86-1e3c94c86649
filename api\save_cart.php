<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $customer_id = $_SESSION['customer_id'];
    $cart_json = $_POST['cart'] ?? '';
    
    if (empty($cart_json)) {
        echo json_encode(['success' => false, 'message' => 'Empty cart data']);
        exit;
    }
    
    $cart = json_decode($cart_json, true);
    
    if (!is_array($cart)) {
        echo json_encode(['success' => false, 'message' => 'Invalid cart data']);
        exit;
    }
    
    // Clear existing cart for this customer
    $stmt = $pdo->prepare("DELETE FROM cart WHERE customer_id = ?");
    $stmt->execute([$customer_id]);
    
    // Save cart to session (for checkout.php)
    $_SESSION['cart'] = [];
    
    // Insert new cart items
    if (!empty($cart)) {
        $stmt = $pdo->prepare("INSERT INTO cart (customer_id, product_id, quantity, created_at) VALUES (?, ?, ?, NOW())");
        
        foreach ($cart as $item) {
            if (isset($item['id']) && isset($item['quantity']) && $item['quantity'] > 0) {
                $product_id = (int)$item['id'];
                $quantity = (int)$item['quantity'];
                
                // Save to database
                $stmt->execute([$customer_id, $product_id, $quantity]);
                
                // Save to session
                $_SESSION['cart'][$product_id] = $quantity;
            }
        }
    }
    
    echo json_encode(['success' => true, 'message' => 'Cart saved successfully']);
    
} catch(PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
} catch(Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
