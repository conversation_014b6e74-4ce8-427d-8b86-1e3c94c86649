# অনলাইন দোকান ব্যবস্থাপনা সিস্টেম

একটি সম্পূর্ণ অনলাইন দোকান ব্যবস্থাপনা সিস্টেম যা PHP, MySQL, JavaScript, AJAX, CSS এবং HTML ব্যবহার করে তৈরি।

## বৈশিষ্ট্যসমূহ

### কাস্টমার ফিচার
- ✅ ব্যবহারকারী রেজিস্ট্রেশন এবং লগইন
- ✅ পণ্য ব্রাউজিং এবং সার্চ
- ✅ ক্যাটেগরি অনুযায়ী ফিল্টারিং
- ✅ শপিং কার্ট
- 🔄 অর্ডার প্লেসমেন্ট
- 🔄 অর্ডার ট্র্যাকিং
- 🔄 প্রোফাইল ম্যানেজমেন্ট

### অ্যাডমিন ফিচার
- ✅ অ্যাডমিন লগইন এবং ড্যাশবোর্ড
- ✅ পণ্য ব্যবস্থাপনা (যোগ, সম্পাদনা, মুছে ফেলা)
- ✅ ক্যাটেগরি ব্যবস্থাপনা
- 🔄 অর্ডার ব্যবস্থাপনা
- 🔄 কাস্টমার ব্যবস্থাপনা
- ✅ ইনভেন্টরি ট্র্যাকিং
- ✅ রিপোর্ট এবং পরিসংখ্যান

### প্রযুক্তিগত বৈশিষ্ট্য
- ✅ রেসপন্সিভ ডিজাইন
- ✅ AJAX ভিত্তিক ইন্টারঅ্যাকশন
- ✅ নিরাপদ ডাটাবেস অপারেশন (PDO)
- ✅ ইমেজ আপলোড সিস্টেম
- ✅ সেশন ম্যানেজমেন্ট
- ✅ ইনপুট ভ্যালিডেশন এবং স্যানিটাইজেশন

## ইনস্টলেশন নির্দেশনা

### প্রয়োজনীয়তা
- XAMPP/WAMP/LAMP (PHP 7.4+ এবং MySQL 5.7+)
- ওয়েব ব্রাউজার

### ধাপসমূহ

1. **XAMPP ইনস্টল করুন** (যদি ইতিমধ্যে না থাকে)
   - [XAMPP ডাউনলোড](https://www.apachefriends.org/download.html) করুন
   - ইনস্টল করুন এবং Apache ও MySQL সার্ভিস চালু করুন

2. **প্রজেক্ট সেটআপ**
   ```bash
   # XAMPP htdocs ফোল্ডারে প্রজেক্ট কপি করুন
   # Windows: C:\xampp\htdocs\dokan
   # Linux/Mac: /opt/lampp/htdocs/dokan
   ```

3. **ডাটাবেস সেটআপ**
   - phpMyAdmin খুলুন: http://localhost/phpmyadmin
   - `database_setup.sql` ফাইলটি ইমপোর্ট করুন
   - অথবা SQL কমান্ড রান করুন:
   ```sql
   CREATE DATABASE dokan_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```
   - তারপর `database_setup.sql` ফাইলের সব কোড রান করুন

4. **কনফিগারেশন**
   - `config/database.php` ফাইলে ডাটাবেস সেটিংস চেক করুন:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_USERNAME', 'root');
   define('DB_PASSWORD', '');
   define('DB_NAME', 'dokan_db');
   ```

5. **ফোল্ডার পারমিশন**
   - `uploads/products/` ফোল্ডারে write permission দিন

6. **ওয়েবসাইট অ্যাক্সেস**
   - ব্রাউজারে যান: http://localhost/dokan
   - অ্যাডমিন প্যানেল: http://localhost/dokan/admin

## ডিফল্ট লগইন তথ্য

### অ্যাডমিন
- **ইউজারনেম:** admin
- **পাসওয়ার্ড:** admin123

## ফোল্ডার স্ট্রাকচার

```
dokan/
├── admin/                  # অ্যাডমিন প্যানেল
│   ├── login.php
│   ├── dashboard.php
│   ├── products.php
│   └── logout.php
├── api/                    # AJAX API ফাইল
│   ├── get_products.php
│   └── get_categories.php
├── assets/                 # স্ট্যাটিক ফাইল
│   ├── css/
│   ├── js/
│   └── images/
├── config/                 # কনফিগারেশন ফাইল
│   └── database.php
├── uploads/                # আপলোড করা ফাইল
│   └── products/
├── index.php              # হোম পেজ
├── products.php           # পণ্যের তালিকা
├── login.php              # কাস্টমার লগইন
├── register.php           # কাস্টমার রেজিস্ট্রেশন
├── database_setup.sql     # ডাটাবেস স্ট্রাকচার
└── README.md
```

## ব্যবহার নির্দেশনা

### কাস্টমারদের জন্য
1. ওয়েবসাইটে যান
2. রেজিস্ট্রেশন করুন বা লগইন করুন
3. পণ্য ব্রাউজ করুন এবং কার্টে যোগ করুন
4. অর্ডার প্লেস করুন

### অ্যাডমিনদের জন্য
1. অ্যাডমিন প্যানেলে লগইন করুন
2. ড্যাশবোর্ডে পরিসংখ্যান দেখুন
3. পণ্য এবং ক্যাটেগরি ম্যানেজ করুন
4. অর্ডার এবং কাস্টমার ম্যানেজ করুন

## নিরাপত্তা বৈশিষ্ট্য

- ✅ SQL Injection প্রতিরোধ (PDO Prepared Statements)
- ✅ XSS প্রতিরোধ (Input Sanitization)
- ✅ CSRF প্রতিরোধ (Session Tokens)
- ✅ পাসওয়ার্ড হ্যাশিং
- ✅ ফাইল আপলোড ভ্যালিডেশন
- ✅ সেশন নিরাপত্তা

## ভবিষ্যত উন্নতি

- 🔄 পেমেন্ট গেটওয়ে ইন্টিগ্রেশন
- 🔄 ইমেইল নোটিফিকেশন
- 🔄 SMS নোটিফিকেশন
- 🔄 ইনভেন্টরি অ্যালার্ট
- 🔄 কুপন এবং ডিসকাউন্ট সিস্টেম
- 🔄 রিভিউ এবং রেটিং
- 🔄 উইশলিস্ট
- 🔄 মাল্টি-ভেন্ডর সাপোর্ট

## সাপোর্ট

কোন সমস্যা বা প্রশ্ন থাকলে GitHub Issues ব্যবহার করুন।

## লাইসেন্স

এই প্রজেক্টটি MIT লাইসেন্সের অধীনে।

---

**নোট:** এটি একটি ডেমো প্রজেক্ট। প্রোডাকশনে ব্যবহারের আগে অতিরিক্ত নিরাপত্তা ব্যবস্থা যোগ করুন।
