<?php
/**
 * Universal Image Handler
 * Supports all major image formats and provides fallback options
 */

class ImageHandler {
    
    // Supported image formats
    private static $supportedFormats = [
        'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg', 'ico', 'tiff', 'tif'
    ];
    
    // MIME types mapping
    private static $mimeTypes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
        'bmp' => 'image/bmp',
        'svg' => 'image/svg+xml',
        'ico' => 'image/x-icon',
        'tiff' => 'image/tiff',
        'tif' => 'image/tiff'
    ];
    
    /**
     * Check if image format is supported
     */
    public static function isSupportedFormat($filename) {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($extension, self::$supportedFormats);
    }
    
    /**
     * Get image MIME type
     */
    public static function getMimeType($filename) {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return self::$mimeTypes[$extension] ?? 'application/octet-stream';
    }
    
    /**
     * Validate uploaded image
     */
    public static function validateImage($file) {
        $errors = [];
        
        // Check if file was uploaded
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            $errors[] = 'No file uploaded';
            return $errors;
        }
        
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'Upload error: ' . self::getUploadErrorMessage($file['error']);
            return $errors;
        }
        
        // Check file size (max 10MB)
        $maxSize = 10 * 1024 * 1024; // 10MB
        if ($file['size'] > $maxSize) {
            $errors[] = 'File size too large. Maximum 10MB allowed.';
        }
        
        // Check file extension
        if (!self::isSupportedFormat($file['name'])) {
            $errors[] = 'Unsupported file format. Supported: ' . implode(', ', self::$supportedFormats);
        }
        
        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $expectedMime = self::getMimeType($file['name']);
        if ($mimeType !== $expectedMime && !self::isValidImageMime($mimeType)) {
            $errors[] = 'Invalid file type detected.';
        }
        
        // Additional validation for non-SVG images
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if ($extension !== 'svg') {
            $imageInfo = @getimagesize($file['tmp_name']);
            if ($imageInfo === false) {
                $errors[] = 'Invalid image file.';
            }
        }
        
        return $errors;
    }
    
    /**
     * Check if MIME type is valid image type
     */
    private static function isValidImageMime($mimeType) {
        $validMimes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp', 
            'image/bmp', 'image/svg+xml', 'image/x-icon', 'image/tiff'
        ];
        return in_array($mimeType, $validMimes);
    }
    
    /**
     * Get upload error message
     */
    private static function getUploadErrorMessage($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize directive';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE directive';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }
    
    /**
     * Generate unique filename
     */
    public static function generateUniqueFilename($originalName, $prefix = '') {
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        $basename = pathinfo($originalName, PATHINFO_FILENAME);
        $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
        $timestamp = time();
        $random = mt_rand(1000, 9999);
        
        return $prefix . $basename . '_' . $timestamp . '_' . $random . '.' . $extension;
    }
    
    /**
     * Move uploaded file to destination
     */
    public static function moveUploadedFile($file, $destination) {
        if (!is_dir(dirname($destination))) {
            mkdir(dirname($destination), 0755, true);
        }
        
        return move_uploaded_file($file['tmp_name'], $destination);
    }
    
    /**
     * Resize image (for non-SVG images)
     */
    public static function resizeImage($sourcePath, $destinationPath, $maxWidth = 800, $maxHeight = 600, $quality = 85) {
        $extension = strtolower(pathinfo($sourcePath, PATHINFO_EXTENSION));
        
        // Skip SVG files
        if ($extension === 'svg') {
            return copy($sourcePath, $destinationPath);
        }
        
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) {
            return false;
        }
        
        list($originalWidth, $originalHeight, $imageType) = $imageInfo;
        
        // Calculate new dimensions
        $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
        if ($ratio >= 1) {
            // No need to resize
            return copy($sourcePath, $destinationPath);
        }
        
        $newWidth = round($originalWidth * $ratio);
        $newHeight = round($originalHeight * $ratio);
        
        // Create source image
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case IMAGETYPE_PNG:
                $sourceImage = imagecreatefrompng($sourcePath);
                break;
            case IMAGETYPE_GIF:
                $sourceImage = imagecreatefromgif($sourcePath);
                break;
            case IMAGETYPE_WEBP:
                if (function_exists('imagecreatefromwebp')) {
                    $sourceImage = imagecreatefromwebp($sourcePath);
                } else {
                    return copy($sourcePath, $destinationPath);
                }
                break;
            case IMAGETYPE_BMP:
                if (function_exists('imagecreatefrombmp')) {
                    $sourceImage = imagecreatefrombmp($sourcePath);
                } else {
                    return copy($sourcePath, $destinationPath);
                }
                break;
            default:
                return copy($sourcePath, $destinationPath);
        }
        
        if (!$sourceImage) {
            return false;
        }
        
        // Create destination image
        $destinationImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($imageType == IMAGETYPE_PNG || $imageType == IMAGETYPE_GIF) {
            imagealphablending($destinationImage, false);
            imagesavealpha($destinationImage, true);
            $transparent = imagecolorallocatealpha($destinationImage, 255, 255, 255, 127);
            imagefilledrectangle($destinationImage, 0, 0, $newWidth, $newHeight, $transparent);
        }
        
        // Resize image
        imagecopyresampled($destinationImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
        
        // Save image
        $result = false;
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg($destinationImage, $destinationPath, $quality);
                break;
            case IMAGETYPE_PNG:
                $result = imagepng($destinationImage, $destinationPath, 9);
                break;
            case IMAGETYPE_GIF:
                $result = imagegif($destinationImage, $destinationPath);
                break;
            case IMAGETYPE_WEBP:
                if (function_exists('imagewebp')) {
                    $result = imagewebp($destinationImage, $destinationPath, $quality);
                }
                break;
            case IMAGETYPE_BMP:
                if (function_exists('imagebmp')) {
                    $result = imagebmp($destinationImage, $destinationPath);
                }
                break;
        }
        
        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($destinationImage);
        
        return $result;
    }
    
    /**
     * Get image display HTML with fallback
     */
    public static function getImageHTML($imagePath, $alt = '', $class = '', $style = '', $productId = null) {
        $fullPath = 'uploads/products/' . $imagePath;
        $imageExists = !empty($imagePath) && file_exists($fullPath);
        
        // Product-specific fallback data
        $fallbackData = self::getFallbackData($productId);
        
        if ($imageExists) {
            $extension = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
            
            if ($extension === 'svg') {
                // For SVG files, embed directly
                return '<object data="' . htmlspecialchars($fullPath) . '" type="image/svg+xml" class="' . htmlspecialchars($class) . '" style="' . htmlspecialchars($style) . '">
                    <div class="product-image-fallback" style="' . htmlspecialchars($style) . ' background: ' . $fallbackData['color'] . ';">
                        <span class="fallback-icon">' . $fallbackData['icon'] . '</span>
                        <span class="fallback-text">' . htmlspecialchars($alt) . '</span>
                    </div>
                </object>';
            } else {
                // For other image formats
                return '<img src="' . htmlspecialchars($fullPath) . '" 
                             alt="' . htmlspecialchars($alt) . '" 
                             class="' . htmlspecialchars($class) . '" 
                             style="' . htmlspecialchars($style) . '"
                             loading="lazy"
                             onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';">
                        <div class="product-image-fallback" style="display: none; ' . htmlspecialchars($style) . ' background: ' . $fallbackData['color'] . ';">
                            <span class="fallback-icon">' . $fallbackData['icon'] . '</span>
                            <span class="fallback-text">' . htmlspecialchars($alt) . '</span>
                        </div>';
            }
        } else {
            // No image available - show fallback
            return '<div class="product-image-fallback" style="' . htmlspecialchars($style) . ' background: ' . $fallbackData['color'] . ';">
                        <span class="fallback-icon">' . $fallbackData['icon'] . '</span>
                        <span class="fallback-text">' . htmlspecialchars($alt) . '</span>
                    </div>';
        }
    }
    
    /**
     * Get fallback data for product
     */
    private static function getFallbackData($productId) {
        $productIcons = [
            1 => '📱', 2 => '💻', 3 => '👔', 4 => '📚', 5 => '🥜'
        ];

        $colors = ['#667eea', '#764ba2', '#28a745', '#ffc107', '#dc3545'];

        // Ensure productId is valid
        $productId = $productId ? intval($productId) : 1;

        $icon = isset($productIcons[$productId]) ? $productIcons[$productId] : '📦';
        $colorIndex = ($productId - 1) % count($colors);
        $color = isset($colors[$colorIndex]) ? $colors[$colorIndex] : '#667eea';

        return ['icon' => $icon, 'color' => $color];
    }
}

/**
 * Image Upload API Handler
 */
if (isset($_POST['action']) && $_POST['action'] === 'upload_image') {
    header('Content-Type: application/json');

    try {
        if (!isset($_FILES['image'])) {
            throw new Exception('No image file uploaded');
        }

        $file = $_FILES['image'];
        $productId = $_POST['product_id'] ?? null;

        if (!$productId) {
            throw new Exception('Product ID is required');
        }

        // Validate image
        $errors = ImageHandler::validateImage($file);
        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }

        // Generate unique filename
        $filename = ImageHandler::generateUniqueFilename($file['name'], 'product_');
        $destination = 'uploads/products/' . $filename;

        // Move uploaded file
        if (!ImageHandler::moveUploadedFile($file, $destination)) {
            throw new Exception('Failed to move uploaded file');
        }

        // Resize image if needed (except SVG)
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        if ($extension !== 'svg') {
            $resizedPath = 'uploads/products/resized_' . $filename;
            if (ImageHandler::resizeImage($destination, $resizedPath, 800, 600)) {
                unlink($destination); // Remove original
                $filename = 'resized_' . $filename;
            }
        }

        // Update database
        require_once '../config/database.php';
        $stmt = $pdo->prepare("UPDATE products SET image = ? WHERE id = ?");
        $stmt->execute([$filename, $productId]);

        echo json_encode([
            'success' => true,
            'message' => 'Image uploaded successfully',
            'filename' => $filename,
            'url' => 'uploads/products/' . $filename
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit;
}
?>
