<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if salesman is logged in
if (!isset($_SESSION['salesman_id'])) {
    redirect('login.php');
}

$salesman_id = $_SESSION['salesman_id'];

// Get date range (default: current month)
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-01');
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');

try {
    // Daily sales report
    $stmt = $pdo->prepare("SELECT 
                          DATE(created_at) as sale_date,
                          COUNT(*) as total_sales,
                          SUM(final_amount) as total_revenue,
                          AVG(final_amount) as avg_sale
                          FROM pos_sales 
                          WHERE salesman_id = ? AND DATE(created_at) BETWEEN ? AND ?
                          GROUP BY DATE(created_at)
                          ORDER BY sale_date DESC");
    $stmt->execute([$salesman_id, $date_from, $date_to]);
    $daily_sales = $stmt->fetchAll();
    
    // Payment method breakdown
    $stmt = $pdo->prepare("SELECT 
                          payment_method,
                          COUNT(*) as count,
                          SUM(final_amount) as total
                          FROM pos_sales 
                          WHERE salesman_id = ? AND DATE(created_at) BETWEEN ? AND ?
                          GROUP BY payment_method");
    $stmt->execute([$salesman_id, $date_from, $date_to]);
    $payment_methods = $stmt->fetchAll();
    
    // Top selling products
    $stmt = $pdo->prepare("SELECT 
                          p.name as product_name,
                          SUM(psi.quantity) as total_quantity,
                          SUM(psi.total_price) as total_revenue,
                          COUNT(DISTINCT ps.id) as sale_count
                          FROM pos_sale_items psi
                          JOIN pos_sales ps ON psi.sale_id = ps.id
                          JOIN products p ON psi.product_id = p.id
                          WHERE ps.salesman_id = ? AND DATE(ps.created_at) BETWEEN ? AND ?
                          GROUP BY psi.product_id
                          ORDER BY total_quantity DESC
                          LIMIT 10");
    $stmt->execute([$salesman_id, $date_from, $date_to]);
    $top_products = $stmt->fetchAll();
    
    // Hourly sales pattern
    $stmt = $pdo->prepare("SELECT 
                          HOUR(created_at) as hour,
                          COUNT(*) as sales_count,
                          SUM(final_amount) as revenue
                          FROM pos_sales 
                          WHERE salesman_id = ? AND DATE(created_at) BETWEEN ? AND ?
                          GROUP BY HOUR(created_at)
                          ORDER BY hour");
    $stmt->execute([$salesman_id, $date_from, $date_to]);
    $hourly_sales = $stmt->fetchAll();
    
    // Summary statistics
    $stmt = $pdo->prepare("SELECT 
                          COUNT(*) as total_sales,
                          SUM(final_amount) as total_revenue,
                          AVG(final_amount) as avg_sale,
                          SUM(discount_amount) as total_discount,
                          COUNT(DISTINCT DATE(created_at)) as active_days
                          FROM pos_sales 
                          WHERE salesman_id = ? AND DATE(created_at) BETWEEN ? AND ?");
    $stmt->execute([$salesman_id, $date_from, $date_to]);
    $summary = $stmt->fetch();
    
    // Get salesman info for commission calculation
    $stmt = $pdo->prepare("SELECT commission_rate FROM salesman WHERE id = ?");
    $stmt->execute([$salesman_id]);
    $salesman_info = $stmt->fetch();
    $commission_rate = $salesman_info['commission_rate'] ?? 0;
    $estimated_commission = ($summary['total_revenue'] * $commission_rate) / 100;
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    $daily_sales = [];
    $payment_methods = [];
    $top_products = [];
    $hourly_sales = [];
    $summary = ['total_sales' => 0, 'total_revenue' => 0, 'avg_sale' => 0, 'total_discount' => 0, 'active_days' => 0];
    $estimated_commission = 0;
}

$payment_method_names = [
    'cash' => 'নগদ',
    'card' => 'কার্ড',
    'mobile_banking' => 'মোবাইল ব্যাংকিং'
];
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রিপোর্ট - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo h1 {
            font-size: 1.5rem;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .nav-menu {
            background: white;
            padding: 1rem 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .nav-menu ul {
            list-style: none;
            display: flex;
            gap: 2rem;
        }
        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .nav-menu a:hover, .nav-menu a.active {
            background: #667eea;
            color: white;
        }
        .main-content {
            padding: 2rem 0;
        }
        .page-header {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .page-header h2 {
            margin-bottom: 1rem;
            color: #333;
        }
        .date-filter {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .filter-form {
            display: flex;
            gap: 1rem;
            align-items: end;
        }
        .form-group {
            flex: 1;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: background-color 0.3s ease;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card i {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .stat-card h3 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
            color: #333;
        }
        .stat-card p {
            color: #666;
            font-size: 0.9rem;
        }
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        .report-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .report-header {
            padding: 1.5rem;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        .report-header h3 {
            margin: 0;
            color: #333;
        }
        .report-body {
            padding: 1.5rem;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 1rem;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.5rem;
        }
        .progress-fill {
            height: 100%;
            background: #667eea;
            transition: width 0.3s ease;
        }
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            .nav-menu ul {
                flex-wrap: wrap;
                gap: 1rem;
            }
            .filter-form {
                flex-direction: column;
            }
            .reports-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-cash-register"></i> POS সিস্টেম</h1>
                </div>
                <div class="user-info">
                    <span><i class="fas fa-user"></i> <?php echo $_SESSION['salesman_name']; ?></span>
                    <a href="dashboard.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                    </a>
                    <a href="logout.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-sign-out-alt"></i> লগআউট
                    </a>
                </div>
            </div>
        </div>
    </header>

    <nav class="nav-menu">
        <div class="container">
            <ul>
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="pos.php"><i class="fas fa-cash-register"></i> নতুন বিক্রয়</a></li>
                <li><a href="sales.php"><i class="fas fa-list"></i> বিক্রয় তালিকা</a></li>
                <li><a href="products.php"><i class="fas fa-box"></i> পণ্য তালিকা</a></li>
                <li><a href="reports.php" class="active"><i class="fas fa-chart-bar"></i> রিপোর্ট</a></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="page-header">
                <h2><i class="fas fa-chart-bar"></i> বিক্রয় রিপোর্ট</h2>
                <p>আপনার বিক্রয় কর্মক্ষমতা এবং পরিসংখ্যান</p>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Date Filter -->
            <div class="date-filter">
                <form method="GET" class="filter-form">
                    <div class="form-group">
                        <label>শুরুর তারিখ</label>
                        <input type="date" name="date_from" class="form-control" value="<?php echo $date_from; ?>">
                    </div>
                    <div class="form-group">
                        <label>শেষ তারিখ</label>
                        <input type="date" name="date_to" class="form-control" value="<?php echo $date_to; ?>">
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> রিপোর্ট দেখুন
                        </button>
                    </div>
                </form>
            </div>

            <!-- Summary Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-receipt" style="color: #667eea;"></i>
                    <h3><?php echo number_format($summary['total_sales']); ?></h3>
                    <p>মোট বিক্রয়</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-money-bill-wave" style="color: #2ed573;"></i>
                    <h3>৳<?php echo number_format($summary['total_revenue'], 2); ?></h3>
                    <p>মোট আয়</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-chart-line" style="color: #ffa502;"></i>
                    <h3>৳<?php echo number_format($summary['avg_sale'], 2); ?></h3>
                    <p>গড় বিক্রয়</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-percentage" style="color: #ff4757;"></i>
                    <h3>৳<?php echo number_format($estimated_commission, 2); ?></h3>
                    <p>আনুমানিক কমিশন (<?php echo $commission_rate; ?>%)</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-tags" style="color: #9c88ff;"></i>
                    <h3>৳<?php echo number_format($summary['total_discount'], 2); ?></h3>
                    <p>মোট ছাড়</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-calendar-day" style="color: #ff6b6b;"></i>
                    <h3><?php echo $summary['active_days']; ?></h3>
                    <p>সক্রিয় দিন</p>
                </div>
            </div>

            <!-- Reports Grid -->
            <div class="reports-grid">
                <!-- Daily Sales Chart -->
                <div class="report-card">
                    <div class="report-header">
                        <h3><i class="fas fa-calendar-alt"></i> দৈনিক বিক্রয়</h3>
                    </div>
                    <div class="report-body">
                        <div class="chart-container">
                            <canvas id="dailySalesChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Payment Methods -->
                <div class="report-card">
                    <div class="report-header">
                        <h3><i class="fas fa-credit-card"></i> পেমেন্ট পদ্ধতি</h3>
                    </div>
                    <div class="report-body">
                        <div class="chart-container">
                            <canvas id="paymentMethodChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Hourly Sales Pattern -->
                <div class="report-card">
                    <div class="report-header">
                        <h3><i class="fas fa-clock"></i> ঘন্টাভিত্তিক বিক্রয়</h3>
                    </div>
                    <div class="report-body">
                        <div class="chart-container">
                            <canvas id="hourlySalesChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Top Products -->
                <div class="report-card">
                    <div class="report-header">
                        <h3><i class="fas fa-star"></i> জনপ্রিয় পণ্য</h3>
                    </div>
                    <div class="report-body">
                        <?php if (empty($top_products)): ?>
                            <div style="text-align: center; color: #666; padding: 2rem;">
                                <i class="fas fa-box fa-2x" style="margin-bottom: 1rem;"></i>
                                <p>কোন বিক্রয় ডেটা নেই</p>
                            </div>
                        <?php else: ?>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>পণ্য</th>
                                        <th>বিক্রিত</th>
                                        <th>আয়</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $max_quantity = max(array_column($top_products, 'total_quantity'));
                                    foreach ($top_products as $product):
                                        $percentage = $max_quantity > 0 ? ($product['total_quantity'] / $max_quantity) * 100 : 0;
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($product['product_name']); ?></strong>
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: <?php echo $percentage; ?>%;"></div>
                                            </div>
                                        </td>
                                        <td><?php echo $product['total_quantity']; ?> টি</td>
                                        <td>৳<?php echo number_format($product['total_revenue'], 2); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Daily Sales Table -->
            <?php if (!empty($daily_sales)): ?>
            <div class="report-card">
                <div class="report-header">
                    <h3><i class="fas fa-table"></i> দৈনিক বিক্রয় বিস্তারিত</h3>
                </div>
                <div class="report-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>তারিখ</th>
                                <th>বিক্রয় সংখ্যা</th>
                                <th>মোট আয়</th>
                                <th>গড় বিক্রয়</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($daily_sales as $day): ?>
                            <tr>
                                <td><?php echo date('d/m/Y', strtotime($day['sale_date'])); ?></td>
                                <td><?php echo $day['total_sales']; ?></td>
                                <td>৳<?php echo number_format($day['total_revenue'], 2); ?></td>
                                <td>৳<?php echo number_format($day['avg_sale'], 2); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </main>

    <script>
        // Daily Sales Chart
        const dailySalesCtx = document.getElementById('dailySalesChart').getContext('2d');
        const dailySalesData = <?php echo json_encode($daily_sales); ?>;

        new Chart(dailySalesCtx, {
            type: 'line',
            data: {
                labels: dailySalesData.map(item => {
                    const date = new Date(item.sale_date);
                    return date.toLocaleDateString('bn-BD');
                }),
                datasets: [{
                    label: 'দৈনিক আয়',
                    data: dailySalesData.map(item => parseFloat(item.total_revenue)),
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '৳' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Payment Method Chart
        const paymentMethodCtx = document.getElementById('paymentMethodChart').getContext('2d');
        const paymentMethodData = <?php echo json_encode($payment_methods); ?>;
        const paymentMethodNames = <?php echo json_encode($payment_method_names); ?>;

        new Chart(paymentMethodCtx, {
            type: 'doughnut',
            data: {
                labels: paymentMethodData.map(item => paymentMethodNames[item.payment_method] || item.payment_method),
                datasets: [{
                    data: paymentMethodData.map(item => parseFloat(item.total)),
                    backgroundColor: ['#667eea', '#2ed573', '#ffa502', '#ff4757'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Hourly Sales Chart
        const hourlySalesCtx = document.getElementById('hourlySalesChart').getContext('2d');
        const hourlySalesData = <?php echo json_encode($hourly_sales); ?>;

        // Create 24-hour array
        const hourlyData = Array(24).fill(0);
        hourlySalesData.forEach(item => {
            hourlyData[parseInt(item.hour)] = parseFloat(item.revenue);
        });

        new Chart(hourlySalesCtx, {
            type: 'bar',
            data: {
                labels: Array.from({length: 24}, (_, i) => i + ':00'),
                datasets: [{
                    label: 'ঘন্টাভিত্তিক আয়',
                    data: hourlyData,
                    backgroundColor: '#ffa502',
                    borderColor: '#ff9500',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '৳' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
