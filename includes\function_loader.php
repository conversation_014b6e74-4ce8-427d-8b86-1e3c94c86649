<?php
/**
 * Function Loader - Prevents function redeclaration conflicts
 * This file ensures functions are loaded only once across the application
 */

// Prevent multiple inclusions
if (defined('FUNCTION_LOADER_LOADED')) {
    return;
}
define('FUNCTION_LOADER_LOADED', true);

/**
 * Check if a function exists before declaring it
 * This is a helper to make the code cleaner
 */
function declare_function_if_not_exists($function_name, $callback) {
    if (!function_exists($function_name)) {
        $callback();
    }
}

/**
 * Load all utility functions safely
 */
function load_utility_functions() {
    
    // Sanitize input function
    declare_function_if_not_exists('sanitize_input', function() {
        function sanitize_input($data) {
            if (is_array($data)) {
                return array_map('sanitize_input', $data);
            }
            return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
        }
    });

    // Safe array access
    declare_function_if_not_exists('safe_get', function() {
        function safe_get($array, $key, $default = null) {
            return isset($array[$key]) ? $array[$key] : $default;
        }
    });

    // Safe POST data retrieval
    declare_function_if_not_exists('safe_post', function() {
        function safe_post($key, $default = '') {
            return isset($_POST[$key]) ? $_POST[$key] : $default;
        }
    });

    // Safe GET data retrieval
    declare_function_if_not_exists('safe_get_param', function() {
        function safe_get_param($key, $default = '') {
            return isset($_GET[$key]) ? $_GET[$key] : $default;
        }
    });

    // Safe SESSION data retrieval
    declare_function_if_not_exists('safe_session', function() {
        function safe_session($key, $default = null) {
            return isset($_SESSION[$key]) ? $_SESSION[$key] : $default;
        }
    });

    // Validate email
    declare_function_if_not_exists('validate_email', function() {
        function validate_email($email) {
            return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
        }
    });

    // Validate phone
    declare_function_if_not_exists('validate_phone', function() {
        function validate_phone($phone) {
            $phone = preg_replace('/[^0-9]/', '', $phone);
            return preg_match('/^(?:\+88|88)?01[3-9]\d{8}$/', $phone);
        }
    });

    // Format price
    declare_function_if_not_exists('format_price', function() {
        function format_price($price) {
            return '৳' . number_format($price, 2);
        }
    });

    // Format date
    declare_function_if_not_exists('format_date', function() {
        function format_date($date, $format = 'd/m/Y') {
            if (empty($date) || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
                return 'N/A';
            }
            return date($format, strtotime($date));
        }
    });

    // Format datetime
    declare_function_if_not_exists('format_datetime', function() {
        function format_datetime($datetime, $format = 'd/m/Y H:i') {
            if (empty($datetime) || $datetime === '0000-00-00 00:00:00') {
                return 'N/A';
            }
            return date($format, strtotime($datetime));
        }
    });

    // Generate random string
    declare_function_if_not_exists('generate_random_string', function() {
        function generate_random_string($length = 10) {
            $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
            $charactersLength = strlen($characters);
            $randomString = '';
            for ($i = 0; $i < $length; $i++) {
                $randomString .= $characters[rand(0, $charactersLength - 1)];
            }
            return $randomString;
        }
    });

    // Check if empty
    declare_function_if_not_exists('is_empty', function() {
        function is_empty($value) {
            return empty($value) || trim($value) === '';
        }
    });

    // Safe file exists
    declare_function_if_not_exists('safe_file_exists', function() {
        function safe_file_exists($filepath) {
            return !empty($filepath) && file_exists($filepath);
        }
    });

    // Safe array key exists
    declare_function_if_not_exists('safe_array_key_exists', function() {
        function safe_array_key_exists($key, $array) {
            return is_array($array) && array_key_exists($key, $array);
        }
    });

    // Get file extension safely
    declare_function_if_not_exists('safe_get_extension', function() {
        function safe_get_extension($filename) {
            if (empty($filename)) {
                return '';
            }
            return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        }
    });

    // Format bytes
    declare_function_if_not_exists('format_bytes', function() {
        function format_bytes($size, $precision = 2) {
            if ($size === 0) {
                return '0 B';
            }
            
            $units = ['B', 'KB', 'MB', 'GB', 'TB'];
            $base = log($size, 1024);
            
            return round(pow(1024, $base - floor($base)), $precision) . ' ' . $units[floor($base)];
        }
    });

    // Truncate text
    declare_function_if_not_exists('truncate_text', function() {
        function truncate_text($text, $length = 100, $suffix = '...') {
            if (strlen($text) <= $length) {
                return $text;
            }
            return substr($text, 0, $length) . $suffix;
        }
    });

    // Check AJAX request
    declare_function_if_not_exists('is_ajax_request', function() {
        function is_ajax_request() {
            return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                   strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
        }
    });

    // Redirect with message
    declare_function_if_not_exists('redirect_with_message', function() {
        function redirect_with_message($url, $message, $type = 'success') {
            $_SESSION['flash_message'] = $message;
            $_SESSION['flash_type'] = $type;
            header("Location: $url");
            exit;
        }
    });

    // Get flash message
    declare_function_if_not_exists('get_flash_message', function() {
        function get_flash_message() {
            if (isset($_SESSION['flash_message'])) {
                $message = $_SESSION['flash_message'];
                $type = $_SESSION['flash_type'] ?? 'info';
                unset($_SESSION['flash_message'], $_SESSION['flash_type']);
                return ['message' => $message, 'type' => $type];
            }
            return null;
        }
    });

    // Log activity
    declare_function_if_not_exists('log_activity', function() {
        function log_activity($action, $description, $user_id = null, $user_type = 'user') {
            $logMessage = "Activity: $action | User: $user_id ($user_type) | Description: $description";
            error_log($logMessage);
        }
    });

    // Check if logged in
    declare_function_if_not_exists('is_logged_in', function() {
        function is_logged_in($user_type = 'user') {
            switch ($user_type) {
                case 'admin':
                    return isset($_SESSION['admin_id']);
                case 'salesman':
                    return isset($_SESSION['salesman_id']);
                case 'user':
                default:
                    return isset($_SESSION['user_id']);
            }
        }
    });

    // Get current user ID
    declare_function_if_not_exists('get_current_user_id', function() {
        function get_current_user_id($user_type = 'user') {
            switch ($user_type) {
                case 'admin':
                    return safe_session('admin_id');
                case 'salesman':
                    return safe_session('salesman_id');
                case 'user':
                default:
                    return safe_session('user_id');
            }
        }
    });

    // Generate CSRF token
    declare_function_if_not_exists('generate_csrf_token', function() {
        function generate_csrf_token() {
            if (!isset($_SESSION['csrf_token'])) {
                $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            }
            return $_SESSION['csrf_token'];
        }
    });

    // Verify CSRF token
    declare_function_if_not_exists('verify_csrf_token', function() {
        function verify_csrf_token($token) {
            return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
        }
    });
}

// Load all functions
load_utility_functions();

// Function to check what functions are loaded (for debugging)
function get_loaded_functions() {
    $functions = [
        'sanitize_input', 'safe_get', 'safe_post', 'safe_get_param', 'safe_session',
        'validate_email', 'validate_phone', 'format_price', 'format_date', 'format_datetime',
        'generate_random_string', 'is_empty', 'safe_file_exists', 'safe_array_key_exists',
        'safe_get_extension', 'format_bytes', 'truncate_text', 'is_ajax_request',
        'redirect_with_message', 'get_flash_message', 'log_activity', 'is_logged_in',
        'get_current_user_id', 'generate_csrf_token', 'verify_csrf_token'
    ];
    
    $loaded = [];
    foreach ($functions as $func) {
        $loaded[$func] = function_exists($func);
    }
    
    return $loaded;
}
?>
