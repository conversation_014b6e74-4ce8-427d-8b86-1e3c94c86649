<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    redirect('../login.php');
}

$tran_id = $_POST['tran_id'] ?? $_GET['tran_id'] ?? '';
$error_message = $_POST['error'] ?? $_GET['error'] ?? 'পেমেন্ট ব্যর্থ হয়েছে।';

// Log the failed payment
if ($tran_id) {
    log_activity('Payment Failed', "Payment failed for transaction: $tran_id", $_SESSION['customer_id'], 'customer');
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট ব্যর্থ - <?php echo SITE_NAME; ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .payment-result {
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .fail-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        .error-details {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <a href="../index.php">
                    <i class="fas fa-store"></i>
                    <?php echo SITE_NAME; ?>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="payment-result">
            <i class="fas fa-times-circle fail-icon"></i>
            <h2>পেমেন্ট ব্যর্থ!</h2>
            <p>দুঃখিত, আপনার পেমেন্ট সম্পন্ন হয়নি।</p>
            
            <?php if ($tran_id): ?>
            <div class="error-details">
                <strong>লেনদেন আইডি:</strong> <?php echo htmlspecialchars($tran_id); ?><br>
                <strong>কারণ:</strong> <?php echo htmlspecialchars($error_message); ?>
            </div>
            <?php endif; ?>
            
            <div style="margin-top: 2rem;">
                <a href="../checkout.php" class="btn btn-primary">
                    <i class="fas fa-redo"></i> আবার চেষ্টা করুন
                </a>
                <a href="../cart.php" class="btn btn-secondary" style="margin-left: 1rem;">
                    <i class="fas fa-shopping-cart"></i> কার্ট দেখুন
                </a>
                <a href="../index.php" class="btn btn-secondary" style="margin-left: 1rem;">
                    <i class="fas fa-home"></i> হোম পেজ
                </a>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 <?php echo SITE_NAME; ?>. সকল অধিকার সংরক্ষিত।</p>
        </div>
    </footer>
</body>
</html>
