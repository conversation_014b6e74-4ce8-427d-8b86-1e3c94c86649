<?php
// Clean version of edit product page

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Include required files
require_once '../includes/function_loader.php';

// Start session
session_start();

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=dokan_db', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Get product ID
$productId = safe_get_param('id');
if (!$productId) {
    header('Location: products.php');
    exit;
}

// Get product details
try {
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.id = ?");
    $stmt->execute([$productId]);
    $product = $stmt->fetch();
    
    if (!$product) {
        header('Location: products.php');
        exit;
    }
} catch(PDOException $e) {
    die('Error fetching product: ' . $e->getMessage());
}

// Get categories
try {
    $stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
    $categories = $stmt->fetchAll();
} catch(PDOException $e) {
    die('Error fetching categories: ' . $e->getMessage());
}

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && safe_post('action') === 'update_product') {
    try {
        $name = trim(safe_post('name'));
        $description = trim(safe_post('description'));
        $price = floatval(safe_post('price', 0));
        $stock_quantity = intval(safe_post('stock_quantity', 0));
        $category_id = intval(safe_post('category_id', 0));
        $sku = trim(safe_post('sku'));
        $status = safe_post('status', 'active');
        
        // Basic validation
        if (empty($name) || $price <= 0 || empty($category_id)) {
            throw new Exception('নাম, দাম এবং ক্যাটাগরি আবশ্যক।');
        }
        
        // Update product
        $stmt = $pdo->prepare("UPDATE products SET name = ?, description = ?, price = ?, stock_quantity = ?, category_id = ?, sku = ?, status = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$name, $description, $price, $stock_quantity, $category_id, $sku, $status, $productId]);
        
        $message = 'পণ্য সফলভাবে আপডেট করা হয়েছে!';
        $messageType = 'success';
        
        // Refresh product data
        $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.id = ?");
        $stmt->execute([$productId]);
        $product = $stmt->fetch();
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পণ্য সম্পাদনা - <?php echo htmlspecialchars($product['name']); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .required {
            color: #dc3545;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e0e0e0;
        }
        
        .message {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>পণ্য সম্পাদনা</h1>
            <p><?php echo htmlspecialchars($product['name']); ?></p>
        </div>

        <div class="main-content">
            <?php if ($message): ?>
                <div class="message <?php echo $messageType; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <form method="POST" id="editProductForm">
                <input type="hidden" name="action" value="update_product">

                <div class="form-group">
                    <label for="name">পণ্যের নাম <span class="required">*</span></label>
                    <input type="text" id="name" name="name" class="form-control" required value="<?php echo htmlspecialchars($product['name']); ?>">
                </div>

                <div class="form-group">
                    <label for="sku">SKU</label>
                    <input type="text" id="sku" name="sku" class="form-control" value="<?php echo htmlspecialchars($product['sku'] ?? ''); ?>">
                </div>

                <div class="form-group">
                    <label for="description">বিবরণ</label>
                    <textarea id="description" name="description" class="form-control" rows="4"><?php echo htmlspecialchars($product['description'] ?? ''); ?></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="category_id">ক্যাটাগরি <span class="required">*</span></label>
                        <select id="category_id" name="category_id" class="form-control" required>
                            <option value="">ক্যাটাগরি নির্বাচন করুন</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>" <?php echo ($category['id'] == $product['category_id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="status">স্ট্যাটাস</label>
                        <select id="status" name="status" class="form-control">
                            <option value="active" <?php echo ($product['status'] == 'active') ? 'selected' : ''; ?>>সক্রিয়</option>
                            <option value="inactive" <?php echo ($product['status'] == 'inactive') ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="price">দাম (৳) <span class="required">*</span></label>
                        <input type="number" id="price" name="price" class="form-control" step="0.01" min="0" required value="<?php echo $product['price']; ?>">
                    </div>

                    <div class="form-group">
                        <label for="stock_quantity">স্টক পরিমাণ</label>
                        <input type="number" id="stock_quantity" name="stock_quantity" class="form-control" min="0" value="<?php echo $product['stock_quantity']; ?>">
                    </div>
                </div>

                <div class="form-actions">
                    <a href="products.php" class="btn btn-secondary">বাতিল</a>
                    <button type="submit" class="btn btn-primary">পরিবর্তন সংরক্ষণ</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Form validation
        document.getElementById('editProductForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const price = document.getElementById('price').value;
            const categoryId = document.getElementById('category_id').value;
            
            if (!name) {
                e.preventDefault();
                alert('পণ্যের নাম আবশ্যক।');
                document.getElementById('name').focus();
                return;
            }
            
            if (!price || parseFloat(price) <= 0) {
                e.preventDefault();
                alert('সঠিক দাম দিন।');
                document.getElementById('price').focus();
                return;
            }
            
            if (!categoryId) {
                e.preventDefault();
                alert('ক্যাটাগরি নির্বাচন করুন।');
                document.getElementById('category_id').focus();
                return;
            }
        });
    </script>
</body>
</html>
