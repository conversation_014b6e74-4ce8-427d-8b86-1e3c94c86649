<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if salesman is logged in
if (!isset($_SESSION['salesman_id'])) {
    redirect('login.php');
}

$salesman_id = $_SESSION['salesman_id'];
$sale_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$sale_id) {
    redirect('sales.php');
}

try {
    // Get sale details
    $stmt = $pdo->prepare("SELECT ps.*, s.name as salesman_name, s.employee_id 
                          FROM pos_sales ps 
                          LEFT JOIN salesman s ON ps.salesman_id = s.id 
                          WHERE ps.id = ? AND ps.salesman_id = ?");
    $stmt->execute([$sale_id, $salesman_id]);
    $sale = $stmt->fetch();
    
    if (!$sale) {
        redirect('sales.php');
    }
    
    // Get sale items
    $stmt = $pdo->prepare("SELECT psi.*, p.name as product_name, p.image, c.name as category_name
                          FROM pos_sale_items psi 
                          LEFT JOIN products p ON psi.product_id = p.id
                          LEFT JOIN categories c ON p.category_id = c.id
                          WHERE psi.sale_id = ?");
    $stmt->execute([$sale_id]);
    $sale_items = $stmt->fetchAll();
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    redirect('sales.php');
}

$payment_methods = [
    'cash' => 'নগদ',
    'card' => 'কার্ড',
    'mobile_banking' => 'মোবাইল ব্যাংকিং'
];
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিক্রয় বিস্তারিত - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo h1 {
            font-size: 1.5rem;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .main-content {
            padding: 2rem 0;
        }
        .back-link {
            margin-bottom: 1rem;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
        .receipt {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
        }
        .receipt-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .receipt-header h1 {
            margin-bottom: 0.5rem;
        }
        .receipt-header p {
            opacity: 0.9;
        }
        .receipt-body {
            padding: 2rem;
        }
        .sale-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #e9ecef;
        }
        .info-section h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        .info-label {
            color: #666;
            font-weight: 500;
        }
        .info-value {
            color: #333;
            font-weight: 600;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 2rem;
        }
        .items-table th,
        .items-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .items-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        .product-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .product-image {
            width: 40px;
            height: 40px;
            border-radius: 5px;
            object-fit: cover;
        }
        .product-details h4 {
            margin-bottom: 0.25rem;
            color: #333;
        }
        .product-details small {
            color: #666;
        }
        .summary {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin-top: 2rem;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        .summary-total {
            font-size: 1.2rem;
            font-weight: bold;
            border-top: 2px solid #ddd;
            padding-top: 0.5rem;
            margin-top: 0.5rem;
        }
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .badge-success {
            background: #d4edda;
            color: #155724;
        }
        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #e9ecef;
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 0 0.5rem;
            transition: background-color 0.3s ease;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.9;
        }
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            .sale-info {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            .items-table {
                font-size: 0.9rem;
            }
            .items-table th,
            .items-table td {
                padding: 0.5rem;
            }
            .product-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
        @media print {
            .header, .back-link, .actions {
                display: none;
            }
            .receipt {
                box-shadow: none;
                border: 1px solid #ddd;
            }
            body {
                background: white;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-cash-register"></i> POS সিস্টেম</h1>
                </div>
                <div class="user-info">
                    <span><i class="fas fa-user"></i> <?php echo $_SESSION['salesman_name']; ?></span>
                    <a href="dashboard.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                    </a>
                    <a href="logout.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-sign-out-alt"></i> লগআউট
                    </a>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="back-link">
                <a href="sales.php">
                    <i class="fas fa-arrow-left"></i> বিক্রয় তালিকায় ফিরে যান
                </a>
            </div>

            <div class="receipt">
                <div class="receipt-header">
                    <h1><i class="fas fa-receipt"></i> বিক্রয় রসিদ</h1>
                    <p>বিক্রয় নম্বর: <?php echo $sale['sale_number']; ?></p>
                </div>

                <div class="receipt-body">
                    <!-- Sale Information -->
                    <div class="sale-info">
                        <div class="info-section">
                            <h3><i class="fas fa-info-circle"></i> বিক্রয় তথ্য</h3>
                            <div class="info-item">
                                <span class="info-label">বিক্রয় নম্বর:</span>
                                <span class="info-value"><?php echo $sale['sale_number']; ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">তারিখ:</span>
                                <span class="info-value"><?php echo date('d/m/Y H:i', strtotime($sale['created_at'])); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">পেমেন্ট পদ্ধতি:</span>
                                <span class="info-value">
                                    <span class="badge badge-success">
                                        <?php echo $payment_methods[$sale['payment_method']]; ?>
                                    </span>
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">স্ট্যাটাস:</span>
                                <span class="info-value">
                                    <span class="badge badge-info">
                                        <?php echo ucfirst($sale['payment_status']); ?>
                                    </span>
                                </span>
                            </div>
                        </div>

                        <div class="info-section">
                            <h3><i class="fas fa-user"></i> কাস্টমার তথ্য</h3>
                            <div class="info-item">
                                <span class="info-label">নাম:</span>
                                <span class="info-value">
                                    <?php echo $sale['customer_name'] ? htmlspecialchars($sale['customer_name']) : 'ওয়াক-ইন কাস্টমার'; ?>
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">ফোন:</span>
                                <span class="info-value">
                                    <?php echo $sale['customer_phone'] ? htmlspecialchars($sale['customer_phone']) : '-'; ?>
                                </span>
                            </div>
                        </div>

                        <div class="info-section">
                            <h3><i class="fas fa-user-tie"></i> সেলসম্যান তথ্য</h3>
                            <div class="info-item">
                                <span class="info-label">নাম:</span>
                                <span class="info-value"><?php echo htmlspecialchars($sale['salesman_name']); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">কর্মচারী আইডি:</span>
                                <span class="info-value"><?php echo htmlspecialchars($sale['employee_id']); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Sale Items -->
                    <h3><i class="fas fa-list"></i> বিক্রিত পণ্যসমূহ</h3>
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>পণ্য</th>
                                <th>একক দাম</th>
                                <th>পরিমাণ</th>
                                <th>মোট</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($sale_items as $item): ?>
                            <tr>
                                <td>
                                    <div class="product-info">
                                        <?php if ($item['image']): ?>
                                            <img src="../uploads/products/<?php echo $item['image']; ?>" 
                                                 alt="<?php echo htmlspecialchars($item['product_name']); ?>" 
                                                 class="product-image">
                                        <?php else: ?>
                                            <div style="width: 40px; height: 40px; background: #f8f9fa; border-radius: 5px; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-box" style="color: #ccc;"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="product-details">
                                            <h4><?php echo htmlspecialchars($item['product_name']); ?></h4>
                                            <small><?php echo htmlspecialchars($item['category_name']); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>৳<?php echo number_format($item['unit_price'], 2); ?></td>
                                <td><?php echo $item['quantity']; ?></td>
                                <td><strong>৳<?php echo number_format($item['total_price'], 2); ?></strong></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <!-- Summary -->
                    <div class="summary">
                        <div class="summary-row">
                            <span>সাবটোটাল:</span>
                            <span>৳<?php echo number_format($sale['total_amount'], 2); ?></span>
                        </div>
                        <?php if ($sale['discount_amount'] > 0): ?>
                        <div class="summary-row">
                            <span>ছাড়:</span>
                            <span style="color: #dc3545;">-৳<?php echo number_format($sale['discount_amount'], 2); ?></span>
                        </div>
                        <?php endif; ?>
                        <div class="summary-row summary-total">
                            <span>চূড়ান্ত মোট:</span>
                            <span>৳<?php echo number_format($sale['final_amount'], 2); ?></span>
                        </div>
                    </div>

                    <?php if ($sale['notes']): ?>
                    <div style="margin-top: 1.5rem; padding: 1rem; background: #fff3cd; border-radius: 5px;">
                        <strong><i class="fas fa-sticky-note"></i> নোট:</strong><br>
                        <?php echo nl2br(htmlspecialchars($sale['notes'])); ?>
                    </div>
                    <?php endif; ?>

                    <!-- Actions -->
                    <div class="actions">
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="fas fa-print"></i> প্রিন্ট করুন
                        </button>
                        <a href="sales.php" class="btn btn-secondary">
                            <i class="fas fa-list"></i> বিক্রয় তালিকা
                        </a>
                        <a href="pos.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> নতুন বিক্রয়
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
