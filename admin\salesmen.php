<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

$success = '';
$error = '';

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $salesman_id = (int)$_GET['id'];
    
    try {
        // Check if salesman has any sales
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM pos_sales WHERE salesman_id = ?");
        $stmt->execute([$salesman_id]);
        $sales_count = $stmt->fetchColumn();
        
        if ($sales_count > 0) {
            $error = 'এই সেলসম্যানের বিক্রয় রেকর্ড আছে, তাই ডিলিট করা যাবে না';
        } else {
            $stmt = $pdo->prepare("DELETE FROM salesman WHERE id = ?");
            if ($stmt->execute([$salesman_id])) {
                $success = 'সেলসম্যান সফলভাবে ডিলিট হয়েছে';
            } else {
                $error = 'সেলসম্যান ডিলিট করতে সমস্যা হয়েছে';
            }
        }
    } catch(PDOException $e) {
        $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    }
}

// Get all salesmen
try {
    $stmt = $pdo->query("SELECT s.*, 
                        COUNT(ps.id) as total_sales,
                        COALESCE(SUM(ps.final_amount), 0) as total_revenue
                        FROM salesman s
                        LEFT JOIN pos_sales ps ON s.id = ps.salesman_id
                        GROUP BY s.id
                        ORDER BY s.created_at DESC");
    $salesmen = $stmt->fetchAll();
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    $salesmen = [];
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সেলসম্যান ব্যবস্থাপনা - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo h1 {
            font-size: 1.5rem;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .main-content {
            padding: 2rem 0;
        }
        .page-header {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .page-header h2 {
            margin: 0;
            color: #333;
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .btn-success {
            background: #2ed573;
            color: white;
        }
        .btn-success:hover {
            background: #26d065;
        }
        .btn-warning {
            background: #ffa502;
            color: white;
        }
        .btn-warning:hover {
            background: #ff9500;
        }
        .btn-danger {
            background: #ff4757;
            color: white;
        }
        .btn-danger:hover {
            background: #ff3838;
        }
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
        }
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .badge-success {
            background: #d4edda;
            color: #155724;
        }
        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            font-weight: 500;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .actions {
            display: flex;
            gap: 0.5rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .stat-card h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #333;
        }
        .stat-card p {
            color: #666;
            font-size: 0.9rem;
        }
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            .page-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            .table-container {
                overflow-x: auto;
            }
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-shield-alt"></i> অ্যাডমিন প্যানেল</h1>
                </div>
                <div class="user-info">
                    <span><i class="fas fa-user"></i> <?php echo $_SESSION['admin_name']; ?></span>
                    <a href="dashboard.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                    </a>
                    <a href="logout.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-sign-out-alt"></i> লগআউট
                    </a>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="page-header">
                <h2><i class="fas fa-users"></i> সেলসম্যান ব্যবস্থাপনা</h2>
                <a href="salesman_add.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> নতুন সেলসম্যান যোগ করুন
                </a>
            </div>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-users" style="color: #667eea;"></i>
                    <h3><?php echo count($salesmen); ?></h3>
                    <p>মোট সেলসম্যান</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-user-check" style="color: #2ed573;"></i>
                    <h3><?php echo count(array_filter($salesmen, function($s) { return $s['status'] == 'active'; })); ?></h3>
                    <p>সক্রিয় সেলসম্যান</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-chart-line" style="color: #ffa502;"></i>
                    <h3><?php echo array_sum(array_column($salesmen, 'total_sales')); ?></h3>
                    <p>মোট বিক্রয়</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-money-bill-wave" style="color: #ff4757;"></i>
                    <h3>৳<?php echo number_format(array_sum(array_column($salesmen, 'total_revenue')), 2); ?></h3>
                    <p>মোট আয়</p>
                </div>
            </div>

            <!-- Salesmen Table -->
            <div class="table-container">
                <?php if (empty($salesmen)): ?>
                    <div style="padding: 3rem; text-align: center; color: #666;">
                        <i class="fas fa-users fa-3x" style="margin-bottom: 1rem;"></i>
                        <h3>কোন সেলসম্যান নেই</h3>
                        <p>নতুন সেলসম্যান যোগ করুন</p>
                    </div>
                <?php else: ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>নাম</th>
                                <th>ইমেইল</th>
                                <th>ফোন</th>
                                <th>কর্মচারী আইডি</th>
                                <th>কমিশন</th>
                                <th>বিক্রয়</th>
                                <th>আয়</th>
                                <th>স্ট্যাটাস</th>
                                <th>অ্যাকশন</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($salesmen as $salesman): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($salesman['name']); ?></strong>
                                </td>
                                <td><?php echo htmlspecialchars($salesman['email']); ?></td>
                                <td><?php echo htmlspecialchars($salesman['phone'] ?: '-'); ?></td>
                                <td><?php echo htmlspecialchars($salesman['employee_id']); ?></td>
                                <td><?php echo $salesman['commission_rate']; ?>%</td>
                                <td><?php echo $salesman['total_sales']; ?></td>
                                <td>৳<?php echo number_format($salesman['total_revenue'], 2); ?></td>
                                <td>
                                    <span class="badge <?php echo $salesman['status'] == 'active' ? 'badge-success' : 'badge-warning'; ?>">
                                        <?php echo $salesman['status'] == 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="actions">
                                        <a href="salesman_edit.php?id=<?php echo $salesman['id']; ?>" 
                                           class="btn btn-warning btn-sm" title="সম্পাদনা">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if ($salesman['total_sales'] == 0): ?>
                                        <a href="?action=delete&id=<?php echo $salesman['id']; ?>" 
                                           class="btn btn-danger btn-sm" title="ডিলিট"
                                           onclick="return confirm('আপনি কি নিশ্চিত যে এই সেলসম্যানকে ডিলিট করতে চান?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </main>
</body>
</html>
