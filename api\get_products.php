<?php
header('Content-Type: application/json');
require_once '../config/database.php';

try {
    // Get parameters
    $featured = isset($_GET['featured']) ? (bool)$_GET['featured'] : false;
    $category_id = isset($_GET['category']) ? (int)$_GET['category'] : null;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
    $search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';

    // Build query
    $sql = "SELECT p.*, c.name as category_name FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.status = 'active'";
    
    $params = [];
    
    if ($featured) {
        $sql .= " AND p.featured = 1";
    }
    
    if ($category_id) {
        $sql .= " AND p.category_id = ?";
        $params[] = $category_id;
    }
    
    if ($search) {
        $sql .= " AND (p.name LIKE ? OR p.description LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $sql .= " ORDER BY p.created_at DESC LIMIT $limit OFFSET $offset";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll();
    
    // Get total count for pagination
    $countSql = "SELECT COUNT(*) FROM products p WHERE p.status = 'active'";
    $countParams = [];
    
    if ($featured) {
        $countSql .= " AND p.featured = 1";
    }
    
    if ($category_id) {
        $countSql .= " AND p.category_id = ?";
        $countParams[] = $category_id;
    }
    
    if ($search) {
        $countSql .= " AND (p.name LIKE ? OR p.description LIKE ?)";
        $searchTerm = "%$search%";
        $countParams[] = $searchTerm;
        $countParams[] = $searchTerm;
    }
    
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($countParams);
    $totalCount = $countStmt->fetchColumn();
    
    echo json_encode([
        'success' => true,
        'data' => $products,
        'total' => $totalCount,
        'limit' => $limit,
        'offset' => $offset
    ]);
    
} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'ডাটাবেস এরর: ' . $e->getMessage()
    ]);
} catch(Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'সার্ভার এরর: ' . $e->getMessage()
    ]);
}
?>
