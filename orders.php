<?php
require_once 'config/session.php';
require_once 'config/database.php';
require_once 'config/functions.php';

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    redirect('login.php');
}

$customer_id = $_SESSION['customer_id'];

// Get customer info
try {
    $stmt = $pdo->prepare("SELECT phone FROM customers WHERE id = ?");
    $stmt->execute([$customer_id]);
    $customer = $stmt->fetch();
} catch(PDOException $e) {
    $customer = null;
}

// Get customer orders
try {
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE customer_id = ? ORDER BY created_at DESC");
    $stmt->execute([$customer_id]);
    $orders = $stmt->fetchAll();
} catch(PDOException $e) {
    $orders = [];
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
}

// Function to get order items
function getOrderItems($pdo, $order_id) {
    try {
        $stmt = $pdo->prepare("
            SELECT oi.*, p.name, p.image 
            FROM order_items oi 
            JOIN products p ON oi.product_id = p.id 
            WHERE oi.order_id = ?
        ");
        $stmt->execute([$order_id]);
        return $stmt->fetchAll();
    } catch(PDOException $e) {
        return [];
    }
}

// Function to get status badge
function getStatusBadge($status) {
    $badges = [
        'pending' => '<span style="background: #fff3cd; color: #856404; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">অপেক্ষমান</span>',
        'confirmed' => '<span style="background: #cce5ff; color: #004085; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">নিশ্চিত</span>',
        'processing' => '<span style="background: #e2e3e5; color: #383d41; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">প্রক্রিয়াধীন</span>',
        'shipped' => '<span style="background: #d1ecf1; color: #0c5460; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">পাঠানো হয়েছে</span>',
        'delivered' => '<span style="background: #d4edda; color: #155724; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">ডেলিভার হয়েছে</span>',
        'cancelled' => '<span style="background: #f8d7da; color: #721c24; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">বাতিল</span>'
    ];
    return $badges[$status] ?? $status;
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>আমার অর্ডার - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .orders-container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .order-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        .order-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        .order-info {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }
        .order-body {
            padding: 1.5rem;
        }
        .order-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .item-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 5px;
            margin-right: 1rem;
        }
        .item-details {
            flex: 1;
        }
        .item-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .item-price {
            color: #666;
            font-size: 0.9rem;
        }
        .order-total {
            text-align: right;
            font-size: 1.1rem;
            font-weight: bold;
            color: #2c5aa0;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 2px solid #eee;
        }
        .empty-orders {
            text-align: center;
            padding: 4rem 2rem;
            color: #666;
        }
        .empty-orders i {
            font-size: 4rem;
            margin-bottom: 1rem;
            display: block;
            color: #ddd;
        }
        @media (max-width: 768px) {
            .order-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            .order-info {
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <a href="index.php">
                    <i class="fas fa-store"></i>
                    <?php echo SITE_NAME; ?>
                </a>
            </div>
            <nav class="nav-menu">
                <a href="index.php"><i class="fas fa-home"></i> হোম</a>
                <a href="cart.php"><i class="fas fa-shopping-cart"></i> কার্ট</a>
                <a href="orders.php" class="active"><i class="fas fa-list"></i> অর্ডার</a>
                <a href="logout.php"><i class="fas fa-sign-out-alt"></i> লগআউট</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="orders-container">
            <h2><i class="fas fa-list"></i> আমার অর্ডার</h2>
            
            <?php if (isset($error)): ?>
                <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if (empty($orders)): ?>
                <div class="order-card">
                    <div class="empty-orders">
                        <i class="fas fa-shopping-bag"></i>
                        <h3>কোন অর্ডার নেই</h3>
                        <p>আপনি এখনও কোন অর্ডার করেননি।</p>
                        <a href="index.php" class="btn btn-primary">
                            <i class="fas fa-shopping-cart"></i> কেনাকাটা শুরু করুন
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($orders as $order): ?>
                    <div class="order-card">
                        <div class="order-header">
                            <div class="order-info">
                                <div>
                                    <strong>অর্ডার #<?php echo $order['id']; ?></strong>
                                </div>
                                <div>
                                    <i class="fas fa-calendar"></i> 
                                    <?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?>
                                </div>
                                <div>
                                    <?php echo getStatusBadge($order['status']); ?>
                                </div>
                            </div>
                            <div>
                                <strong><?php echo format_price($order['total_amount']); ?></strong>
                            </div>
                        </div>
                        
                        <div class="order-body">
                            <?php if ($order['shipping_address']): ?>
                                <div style="margin-bottom: 1rem;">
                                    <strong><i class="fas fa-map-marker-alt"></i> ডেলিভারি ঠিকানা:</strong><br>
                                    <?php echo nl2br(htmlspecialchars($order['shipping_address'])); ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($customer && $customer['phone']): ?>
                                <div style="margin-bottom: 1rem;">
                                    <strong><i class="fas fa-phone"></i> ফোন:</strong>
                                    <?php echo htmlspecialchars($customer['phone']); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($order['notes']): ?>
                                <div style="margin-bottom: 1rem;">
                                    <strong><i class="fas fa-sticky-note"></i> নোট:</strong> 
                                    <?php echo nl2br(htmlspecialchars($order['notes'])); ?>
                                </div>
                            <?php endif; ?>
                            
                            <h4><i class="fas fa-box"></i> অর্ডার আইটেম:</h4>
                            <?php 
                            $order_items = getOrderItems($pdo, $order['id']);
                            if ($order_items): 
                            ?>
                                <?php foreach ($order_items as $item): ?>
                                    <div class="order-item">
                                        <img src="<?php echo $item['image'] ? 'uploads/products/' . $item['image'] : 'assets/images/no-image.jpg'; ?>" 
                                             alt="<?php echo htmlspecialchars($item['name']); ?>" class="item-image">
                                        <div class="item-details">
                                            <div class="item-name"><?php echo htmlspecialchars($item['name']); ?></div>
                                            <div class="item-price">
                                                <?php echo format_price($item['price']); ?> × <?php echo $item['quantity']; ?> = 
                                                <strong><?php echo format_price($item['price'] * $item['quantity']); ?></strong>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p style="color: #666; font-style: italic;">অর্ডার আইটেম লোড করা যায়নি।</p>
                            <?php endif; ?>
                            
                            <div class="order-total">
                                মোট: <?php echo format_price($order['total_amount']); ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 <?php echo SITE_NAME; ?>. সকল অধিকার সংরক্ষিত।</p>
        </div>
    </footer>
</body>
</html>
