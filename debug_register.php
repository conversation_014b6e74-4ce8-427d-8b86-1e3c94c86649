<?php
require_once 'config/session.php';
require_once 'config/database.php';
require_once 'config/functions.php';

// Redirect if already logged in
if (isset($_SESSION['customer_id'])) {
    redirect('index.php');
}

$error = '';
$success = '';

echo "<h2>Registration Debug</h2>";

if ($_POST) {
    echo "<h3>Form Submitted!</h3>";
    echo "<pre>POST Data: " . print_r($_POST, true) . "</pre>";
    
    // Check if all required fields are present
    $required_fields = ['name', 'phone', 'password', 'confirm_password'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field])) {
            $error = "Required field missing: $field";
            break;
        }
    }

    if (!isset($error)) {
        $name = sanitize_input($_POST['name']);
        $email = sanitize_input($_POST['email'] ?? '');
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];
        $phone = sanitize_phone($_POST['phone']);
        $address = sanitize_input($_POST['address'] ?? '');
        $city = sanitize_input($_POST['city'] ?? '');
        $postal_code = sanitize_input($_POST['postal_code'] ?? '');

        echo "<h3>Processed Data:</h3>";
        echo "<ul>";
        echo "<li>Name: " . htmlspecialchars($name) . "</li>";
        echo "<li>Phone: " . htmlspecialchars($phone) . "</li>";
        echo "<li>Email: " . htmlspecialchars($email) . "</li>";
        echo "<li>Password Length: " . strlen($password) . "</li>";
        echo "<li>Address: " . htmlspecialchars($address) . "</li>";
        echo "<li>City: " . htmlspecialchars($city) . "</li>";
        echo "<li>Postal Code: " . htmlspecialchars($postal_code) . "</li>";
        echo "</ul>";

        // Handle profile image upload
        $profile_image = null;
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            echo "<h3>File Upload Detected</h3>";
            $upload_result = upload_image($_FILES['profile_image'], 'uploads/customers/');
            if ($upload_result['success']) {
                $profile_image = $upload_result['filename'];
                echo "<p>✅ Image uploaded: $profile_image</p>";
            } else {
                $error = $upload_result['message'];
                echo "<p>❌ Upload error: $error</p>";
            }
        }
        
        // Validation (only if no upload error)
        if (!isset($error)) {
            echo "<h3>Validation:</h3>";
            
            if (empty($name) || empty($phone) || empty($password)) {
                $error = 'নাম, মোবাইল নম্বর এবং পাসওয়ার্ড আবশ্যক।';
                echo "<p>❌ Empty required fields</p>";
            } elseif ($password !== $confirm_password) {
                $error = 'পাসওয়ার্ড মিলছে না।';
                echo "<p>❌ Password mismatch</p>";
            } elseif (strlen($password) < 6) {
                $error = 'পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে।';
                echo "<p>❌ Password too short</p>";
            } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $error = 'সঠিক ইমেইল ঠিকানা দিন।';
                echo "<p>❌ Invalid email</p>";
            } elseif (strlen($phone) < 11) {
                $error = 'মোবাইল নম্বর কমপক্ষে ১১ সংখ্যার হতে হবে।';
                echo "<p>❌ Phone too short</p>";
            } elseif (!validate_bd_mobile($phone)) {
                $error = 'সঠিক বাংলাদেশী মোবাইল নম্বর দিন (01XXXXXXXXX)।';
                echo "<p>❌ Invalid BD mobile</p>";
            } else {
                echo "<p>✅ All validations passed</p>";
                
                try {
                    echo "<h3>Database Checks:</h3>";
                    
                    // Check if phone already exists
                    $stmt = $pdo->prepare("SELECT id FROM customers WHERE phone = ?");
                    $stmt->execute([$phone]);
                    if ($stmt->fetch()) {
                        $error = 'এই মোবাইল নম্বর দিয়ে ইতিমধ্যে একটি অ্যাকাউন্ট রয়েছে।';
                        echo "<p>❌ Phone already exists</p>";
                    } elseif (!empty($email)) {
                        // Check if email already exists (only if email is provided)
                        $stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ?");
                        $stmt->execute([$email]);
                        if ($stmt->fetch()) {
                            $error = 'এই ইমেইল ঠিকানা দিয়ে ইতিমধ্যে একটি অ্যাকাউন্ট রয়েছে।';
                            echo "<p>❌ Email already exists</p>";
                        } else {
                            echo "<p>✅ Email is unique</p>";
                        }
                    } else {
                        echo "<p>✅ Phone is unique</p>";
                    }

                    if (!isset($error)) {
                        echo "<h3>Creating Customer:</h3>";
                        
                        // Create new customer
                        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                        $email_value = !empty($email) ? $email : null;
                        $stmt = $pdo->prepare("INSERT INTO customers (name, email, password, phone, profile_image, address, city, postal_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                        $result = $stmt->execute([$name, $email_value, $hashed_password, $phone, $profile_image, $address, $city, $postal_code]);
                        
                        if ($result) {
                            $customer_id = $pdo->lastInsertId();
                            echo "<p>✅ Customer created with ID: $customer_id</p>";
                            
                            // Log the registration
                            log_activity('Customer Registration', "New customer registered: $name ($phone)", $customer_id, 'customer');
                            echo "<p>✅ Activity logged</p>";
                            
                            $success = 'অ্যাকাউন্ট সফলভাবে তৈরি হয়েছে। এখন লগইন করুন।';
                        } else {
                            echo "<p>❌ Failed to create customer</p>";
                            $error = 'অ্যাকাউন্ট তৈরি করতে সমস্যা হয়েছে।';
                        }
                    }
                } catch(PDOException $e) {
                    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
                    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
                }
            }
        }
    }
} else {
    echo "<p>No form submission detected.</p>";
}

if ($error) {
    echo "<div style='color: red; background: #ffe6e6; padding: 10px; margin: 10px 0; border: 1px solid red;'>";
    echo "<strong>Error:</strong> $error";
    echo "</div>";
}

if ($success) {
    echo "<div style='color: green; background: #e6ffe6; padding: 10px; margin: 10px 0; border: 1px solid green;'>";
    echo "<strong>Success:</strong> $success";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Debug</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 2rem auto; padding: 2rem; }
        .form-group { margin-bottom: 1rem; }
        label { display: block; margin-bottom: 0.5rem; font-weight: bold; }
        input, textarea { width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h2>Simple Registration Form</h2>
    
    <form method="POST" enctype="multipart/form-data">
        <div class="form-group">
            <label for="name">পূর্ণ নাম *</label>
            <input type="text" name="name" id="name" required>
        </div>
        
        <div class="form-group">
            <label for="phone">মোবাইল নম্বর *</label>
            <input type="tel" name="phone" id="phone" placeholder="01XXXXXXXXX" required>
        </div>
        
        <div class="form-group">
            <label for="email">ইমেইল ঠিকানা (ঐচ্ছিক)</label>
            <input type="email" name="email" id="email">
        </div>
        
        <div class="form-group">
            <label for="password">পাসওয়ার্ড *</label>
            <input type="password" name="password" id="password" required>
        </div>
        
        <div class="form-group">
            <label for="confirm_password">পাসওয়ার্ড নিশ্চিত করুন *</label>
            <input type="password" name="confirm_password" id="confirm_password" required>
        </div>
        
        <div class="form-group">
            <label for="address">ঠিকানা</label>
            <textarea name="address" id="address" rows="2"></textarea>
        </div>
        
        <div class="form-group">
            <label for="city">শহর</label>
            <input type="text" name="city" id="city">
        </div>
        
        <div class="form-group">
            <label for="postal_code">পোস্টাল কোড</label>
            <input type="text" name="postal_code" id="postal_code">
        </div>
        
        <button type="submit">অ্যাকাউন্ট তৈরি করুন</button>
    </form>
    
    <p><a href="register.php">← Back to Main Registration</a></p>
</body>
</html>
