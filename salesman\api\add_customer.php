<?php
header('Content-Type: application/json');
require_once '../../config/session.php';
require_once '../../config/database.php';

// Check if salesman is logged in
if (!isset($_SESSION['salesman_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    // If JSON decode fails, try to get from POST
    if (!$input) {
        $input = $_POST;
    }

    $name = sanitize_input($input['name'] ?? '');
    $phone = sanitize_phone($input['phone'] ?? '');
    $email = sanitize_input($input['email'] ?? '');
    $address = sanitize_input($input['address'] ?? '');
    $city = sanitize_input($input['city'] ?? '');
    
    // Validation
    if (empty($name)) {
        echo json_encode(['success' => false, 'message' => 'নাম আবশ্যক।']);
        exit;
    }
    
    if (empty($phone)) {
        echo json_encode(['success' => false, 'message' => 'ফোন নম্বর আবশ্যক।']);
        exit;
    }
    
    // Validate phone number format
    if (!preg_match('/^01[3-9]\d{8}$/', $phone)) {
        echo json_encode(['success' => false, 'message' => 'সঠিক ফোন নম্বর দিন (01XXXXXXXXX)।']);
        exit;
    }
    
    // Check if phone already exists
    $stmt = $pdo->prepare("SELECT id FROM customers WHERE phone = ?");
    $stmt->execute([$phone]);
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'এই ফোন নম্বর ইতিমধ্যে ব্যবহৃত হয়েছে।']);
        exit;
    }
    
    // Check if email already exists (if provided)
    if (!empty($email)) {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            echo json_encode(['success' => false, 'message' => 'সঠিক ইমেইল ঠিকানা দিন।']);
            exit;
        }
        
        $stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetch()) {
            echo json_encode(['success' => false, 'message' => 'এই ইমেইল ঠিকানা ইতিমধ্যে ব্যবহৃত হয়েছে।']);
            exit;
        }
    }
    
    // Generate a default password (phone number)
    $default_password = password_hash($phone, PASSWORD_DEFAULT);
    $email_value = !empty($email) ? $email : null;
    
    // Insert new customer
    $stmt = $pdo->prepare("INSERT INTO customers (name, email, password, phone, address, city, status, created_at) 
                          VALUES (?, ?, ?, ?, ?, ?, 'active', NOW())");
    $result = $stmt->execute([$name, $email_value, $default_password, $phone, $address, $city]);
    
    if ($result) {
        $customer_id = $pdo->lastInsertId();
        
        // Get the newly created customer
        $stmt = $pdo->prepare("SELECT id, name, phone, email, address, city FROM customers WHERE id = ?");
        $stmt->execute([$customer_id]);
        $customer = $stmt->fetch();

        // Add default values for customer stats
        $customer['total_orders'] = 0;
        $customer['total_spent'] = 0;
        $customer['last_order'] = null;

        echo json_encode([
            'success' => true,
            'message' => 'নতুন কাস্টমার সফলভাবে যোগ করা হয়েছে।',
            'data' => $customer
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'কাস্টমার যোগ করতে সমস্যা হয়েছে।']);
    }
    
} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'ডাটাবেস এরর: ' . $e->getMessage()
    ]);
} catch(Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'সার্ভার এরর: ' . $e->getMessage()
    ]);
}
?>
