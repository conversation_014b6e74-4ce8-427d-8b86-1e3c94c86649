<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Redirect if already logged in
if (isset($_SESSION['salesman_id'])) {
    redirect('dashboard.php');
}

$error = '';

if ($_POST) {
    $email = sanitize_input($_POST['email']);
    $password = $_POST['password'];
    
    if (empty($email) || empty($password)) {
        $error = 'ইমেইল এবং পাসওয়ার্ড প্রয়োজন।';
    } else {
        try {
            $stmt = $pdo->prepare("SELECT * FROM salesman WHERE email = ? AND status = 'active'");
            $stmt->execute([$email]);
            $salesman = $stmt->fetch();
            
            if ($salesman && password_verify($password, $salesman['password'])) {
                $_SESSION['salesman_id'] = $salesman['id'];
                $_SESSION['salesman_name'] = $salesman['name'];
                $_SESSION['salesman_email'] = $salesman['email'];
                $_SESSION['employee_id'] = $salesman['employee_id'];
                
                log_activity('salesman_login', "Salesman logged in: {$salesman['name']}", $salesman['id'], 'salesman');
                redirect('dashboard.php');
            } else {
                $error = 'ভুল ইমেইল বা পাসওয়ার্ড।';
            }
        } catch(PDOException $e) {
            $error = 'ডাটাবেস এরর: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সেলসম্যান লগইন - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .login-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        .login-header p {
            color: #666;
            font-size: 0.9rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #f5c6cb;
        }
        .back-link {
            text-align: center;
            margin-top: 1.5rem;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
        .pos-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <i class="fas fa-cash-register pos-icon"></i>
            <h1>সেলসম্যান লগইন</h1>
            <p>POS সিস্টেমে প্রবেশ করুন</p>
        </div>
        
        <?php if ($error): ?>
            <div class="error">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="email">
                    <i class="fas fa-envelope"></i> ইমেইল
                </label>
                <input type="email" id="email" name="email" class="form-control" 
                       placeholder="আপনার ইমেইল লিখুন" required 
                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
            </div>
            
            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i> পাসওয়ার্ড
                </label>
                <input type="password" id="password" name="password" class="form-control" 
                       placeholder="আপনার পাসওয়ার্ড লিখুন" required>
            </div>
            
            <button type="submit" class="btn">
                <i class="fas fa-sign-in-alt"></i> লগইন করুন
            </button>
        </form>
        
        <div class="back-link">
            <a href="../index.php">
                <i class="fas fa-arrow-left"></i> মূল সাইটে ফিরে যান
            </a>
        </div>
        
        <div style="margin-top: 1rem; padding: 1rem; background: #e7f3ff; border-radius: 5px; font-size: 0.9rem;">
            <strong>ডেমো লগইন:</strong><br>
            ইমেইল: <EMAIL><br>
            পাসওয়ার্ড: salesman123
        </div>
    </div>
</body>
</html>
