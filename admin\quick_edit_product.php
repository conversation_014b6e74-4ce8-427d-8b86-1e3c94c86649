<?php
require_once '../includes/error_handler.php';
require_once '../config/session.php';
require_once '../config/database.php';

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    if (!isset($_POST['action']) || $_POST['action'] !== 'quick_update') {
        throw new Exception('Invalid action');
    }

    $productId = intval(safe_post('product_id', 0));
    $name = trim(safe_post('name'));
    $price = floatval(safe_post('price', 0));
    $stockQuantity = intval(safe_post('stock_quantity', 0));
    $status = safe_post('status', 'active');

    // Validation
    if (empty($name)) {
        throw new Exception('পণ্যের নাম আবশ্যক।');
    }

    if ($price <= 0) {
        throw new Exception('দাম অবশ্যই ০ এর চেয়ে বেশি হতে হবে।');
    }

    if ($stockQuantity < 0) {
        throw new Exception('স্টক পরিমাণ ০ বা তার বেশি হতে হবে।');
    }

    if (!in_array($status, ['active', 'inactive'])) {
        throw new Exception('অবৈধ স্ট্যাটাস।');
    }

    if (!$productId) {
        throw new Exception('পণ্য ID আবশ্যক।');
    }

    // Check if product exists
    $stmt = $pdo->prepare("SELECT id FROM products WHERE id = ?");
    $stmt->execute([$productId]);
    if (!$stmt->fetch()) {
        throw new Exception('পণ্য পাওয়া যায়নি।');
    }

    // Update product
    $stmt = $pdo->prepare("UPDATE products SET name = ?, price = ?, stock_quantity = ?, status = ?, updated_at = NOW() WHERE id = ?");
    $stmt->execute([$name, $price, $stockQuantity, $status, $productId]);

    echo json_encode([
        'success' => true,
        'message' => 'পণ্য সফলভাবে আপডেট করা হয়েছে!'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'ডাটাবেস এরর: ' . $e->getMessage()
    ]);
}
?>
