<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = sanitize_input($_POST['name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone']);
    $employee_id = sanitize_input($_POST['employee_id']);
    $password = $_POST['password'] ?? '';
    $commission_rate = (float)$_POST['commission_rate'];
    $status = sanitize_input($_POST['status']);
    
    // Validation
    if (empty($name) || empty($email) || empty($employee_id) || empty($password)) {
        $error = 'সকল প্রয়োজনীয় ফিল্ড পূরণ করুন';
    } elseif (strlen($password) < 6) {
        $error = 'পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে';
    } else {
        try {
            // Check if email or employee_id already exists
            $stmt = $pdo->prepare("SELECT id FROM salesman WHERE email = ? OR employee_id = ?");
            $stmt->execute([$email, $employee_id]);
            
            if ($stmt->fetch()) {
                $error = 'এই ইমেইল বা কর্মচারী আইডি ইতিমধ্যে ব্যবহৃত হয়েছে';
            } else {
                // Insert new salesman
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT INTO salesman (name, email, phone, employee_id, password, commission_rate, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
                
                if ($stmt->execute([$name, $email, $phone, $employee_id, $hashed_password, $commission_rate, $status])) {
                    $success = 'নতুন সেলসম্যান সফলভাবে যোগ করা হয়েছে';
                    // Clear form
                    $_POST = [];
                } else {
                    $error = 'সেলসম্যান যোগ করতে সমস্যা হয়েছে';
                }
            }
        } catch(PDOException $e) {
            $error = 'ডাটাবেস এরর: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নতুন সেলসম্যান - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo h1 {
            font-size: 1.5rem;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .main-content {
            padding: 2rem 0;
        }
        .back-link {
            margin-bottom: 2rem;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
        .form-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .form-header h2 {
            margin-bottom: 0.5rem;
        }
        .form-body {
            padding: 2rem;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        .required {
            color: #ff4757;
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        .btn-primary {
            background: #667eea;
            color: white;
            width: 100%;
        }
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
            margin-top: 1rem;
            width: 100%;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            font-weight: 500;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .password-requirements {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 1rem;
            margin-top: 0.5rem;
            font-size: 0.9rem;
        }
        .password-requirements h4 {
            color: #0066cc;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        .password-requirements ul {
            margin: 0;
            padding-left: 1.5rem;
            color: #0066cc;
        }
        .password-requirements li {
            margin-bottom: 0.25rem;
        }
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            .form-grid {
                grid-template-columns: 1fr;
            }
            .form-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-shield-alt"></i> অ্যাডমিন প্যানেল</h1>
                </div>
                <div class="user-info">
                    <span><i class="fas fa-user"></i> <?php echo $_SESSION['admin_name']; ?></span>
                    <a href="dashboard.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                    </a>
                    <a href="logout.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-sign-out-alt"></i> লগআউট
                    </a>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="back-link">
                <a href="salesmen.php">
                    <i class="fas fa-arrow-left"></i> সেলসম্যান তালিকায় ফিরে যান
                </a>
            </div>

            <div class="form-container">
                <div class="form-header">
                    <h2><i class="fas fa-user-plus"></i> নতুন সেলসম্যান যোগ করুন</h2>
                    <p>সেলসম্যানের সকল তথ্য সঠিকভাবে পূরণ করুন</p>
                </div>
                
                <div class="form-body">
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="name">নাম <span class="required">*</span></label>
                                <input type="text" id="name" name="name" class="form-control" 
                                       value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="employee_id">কর্মচারী আইডি <span class="required">*</span></label>
                                <input type="text" id="employee_id" name="employee_id" class="form-control" 
                                       value="<?php echo htmlspecialchars($_POST['employee_id'] ?? ''); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="email">ইমেইল <span class="required">*</span></label>
                                <input type="email" id="email" name="email" class="form-control" 
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="phone">ফোন নম্বর</label>
                                <input type="text" id="phone" name="phone" class="form-control" 
                                       value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                            </div>

                            <div class="form-group">
                                <label for="commission_rate">কমিশন রেট (%)</label>
                                <input type="number" id="commission_rate" name="commission_rate" class="form-control" 
                                       value="<?php echo $_POST['commission_rate'] ?? '5'; ?>" step="0.01" min="0" max="100">
                            </div>

                            <div class="form-group">
                                <label for="status">স্ট্যাটাস</label>
                                <select id="status" name="status" class="form-control">
                                    <option value="active" <?php echo ($_POST['status'] ?? 'active') == 'active' ? 'selected' : ''; ?>>সক্রিয়</option>
                                    <option value="inactive" <?php echo ($_POST['status'] ?? '') == 'inactive' ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                                </select>
                            </div>

                            <div class="form-group full-width">
                                <label for="password">পাসওয়ার্ড <span class="required">*</span></label>
                                <input type="password" id="password" name="password" class="form-control" required minlength="6">
                                <div class="password-requirements">
                                    <h4><i class="fas fa-info-circle"></i> পাসওয়ার্ড নির্দেশনা:</h4>
                                    <ul>
                                        <li>কমপক্ষে ৬ অক্ষরের হতে হবে</li>
                                        <li>বড় ও ছোট হাতের অক্ষর ব্যবহার করুন</li>
                                        <li>সংখ্যা ও বিশেষ চিহ্ন ব্যবহার করুন</li>
                                        <li>সহজে অনুমানযোগ্য পাসওয়ার্ড এড়িয়ে চলুন</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> সেলসম্যান যোগ করুন
                        </button>
                        
                        <a href="salesmen.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> বাতিল করুন
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
