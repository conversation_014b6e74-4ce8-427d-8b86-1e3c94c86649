<?php
header('Content-Type: application/json');
require_once '../config/database.php';

try {
    $sql = "SELECT * FROM categories WHERE status = 'active' ORDER BY name ASC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'data' => $categories
    ]);
    
} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'ডাটাবেস এরর: ' . $e->getMessage()
    ]);
} catch(Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'সার্ভার এরর: ' . $e->getMessage()
    ]);
}
?>
