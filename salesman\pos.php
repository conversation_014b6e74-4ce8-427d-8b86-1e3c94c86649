<?php
session_start();

// Simple login check
if (!isset($_SESSION['salesman_id'])) {
    header('Location: login.php');
    exit;
}

// Include image handler
require_once '../includes/image_handler.php';

// Database connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=dokan_db', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die('Database connection failed');
}

// Get products
$products = [];
try {
    $stmt = $pdo->query("SELECT * FROM products WHERE status = 'active' LIMIT 20");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(Exception $e) {
    // Continue with empty products array
}

// Get customers
$customers = [];
try {
    $stmt = $pdo->query("SELECT * FROM customers WHERE status = 'active' ORDER BY name");
    $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(Exception $e) {
    // Continue with empty customers array
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            color: #333;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            color: #667eea;
            font-size: 2rem;
        }
        .header a {
            background: #667eea;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .header a:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .products-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .cart-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: fit-content;
            max-height: 90vh;
            overflow-y: auto;
        }
        .search-box {
            width: 100%;
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .search-box:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .product-card {
            background: white;
            border: none;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        .product-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 15px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }
        .product-name {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
            font-size: 16px;
        }
        .product-price {
            color: #667eea;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        .stock-info {
            color: #666;
            font-size: 14px;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 15px;
            display: inline-block;
        }
        .stock-available { background: #d4edda; color: #155724; }
        .stock-low { background: #fff3cd; color: #856404; }
        .stock-out { background: #f8d7da; color: #721c24; }
        .btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }
        .btn-primary:hover {
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .btn-small {
            padding: 8px 15px;
            font-size: 14px;
            width: auto;
            margin: 0 5px;
            border-radius: 20px;
        }
        .cart-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            min-width: 400px;
            min-height: 300px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            cursor: default;
            resize: both;
            overflow: hidden;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 20px 30px 15px 30px;
            border-bottom: 2px solid #f0f0f0;
            cursor: move;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px 20px 0 0;
            user-select: none;
        }
        .modal-header h3 {
            color: white;
            margin: 0;
        }
        .modal-body {
            padding: 30px;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        .modal-minimized .modal-body {
            height: 0;
            padding: 0 30px;
        }
        .modal-controls {
            display: flex;
            gap: 5px;
            align-items: center;
        }
        .control-btn {
            color: rgba(255, 255, 255, 0.8);
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 8px;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            user-select: none;
        }
        .control-btn:hover {
            color: white;
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }
        .minimize-btn:hover {
            background: rgba(255, 193, 7, 0.3);
        }
        .maximize-btn:hover {
            background: rgba(40, 167, 69, 0.3);
        }

        /* Resize handles */
        .resize-handle {
            position: absolute;
            background: transparent;
            z-index: 1000;
        }

        .resize-handle-n {
            top: 0;
            left: 10px;
            right: 10px;
            height: 5px;
            cursor: n-resize;
        }

        .resize-handle-s {
            bottom: 0;
            left: 10px;
            right: 10px;
            height: 5px;
            cursor: s-resize;
        }

        .resize-handle-e {
            top: 10px;
            right: 0;
            bottom: 10px;
            width: 5px;
            cursor: e-resize;
        }

        .resize-handle-w {
            top: 10px;
            left: 0;
            bottom: 10px;
            width: 5px;
            cursor: w-resize;
        }

        .resize-handle-ne {
            top: 0;
            right: 0;
            width: 10px;
            height: 10px;
            cursor: ne-resize;
        }

        .resize-handle-nw {
            top: 0;
            left: 0;
            width: 10px;
            height: 10px;
            cursor: nw-resize;
        }

        .resize-handle-se {
            bottom: 0;
            right: 0;
            width: 10px;
            height: 10px;
            cursor: se-resize;
        }

        .resize-handle-sw {
            bottom: 0;
            left: 0;
            width: 10px;
            height: 10px;
            cursor: sw-resize;
        }

        /* Visual resize indicator */
        .resize-indicator {
            position: absolute;
            bottom: 5px;
            right: 5px;
            width: 15px;
            height: 15px;
            background: linear-gradient(-45deg, transparent 40%, #ccc 40%, #ccc 50%, transparent 50%, transparent 60%, #ccc 60%, #ccc 70%, transparent 70%);
            cursor: se-resize;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }

        .modal-content:hover .resize-indicator {
            opacity: 0.8;
        }

        /* Form Navigation */
        .form-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-top: 2px solid #f0f0f0;
            margin-top: 20px;
        }

        .nav-info {
            color: #666;
            font-size: 14px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .nav-btn-prev {
            background: #6c757d;
            color: white;
        }

        .nav-btn-prev:hover:not(:disabled) {
            background: #5a6268;
            transform: translateX(-2px);
        }

        .nav-btn-next {
            background: #667eea;
            color: white;
        }

        .nav-btn-next:hover:not(:disabled) {
            background: #5a6fd8;
            transform: translateX(2px);
        }

        /* Form Steps */
        .form-steps {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            padding: 0 20px;
        }

        .step {
            flex: 1;
            text-align: center;
            position: relative;
            padding: 10px;
        }

        .step::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #e0e0e0;
            z-index: 1;
        }

        .step:first-child::before {
            left: 50%;
        }

        .step:last-child::before {
            right: 50%;
        }

        .step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e0e0e0;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 5px;
            font-weight: bold;
            font-size: 14px;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .step.active .step-circle {
            background: #667eea;
            color: white;
        }

        .step.completed .step-circle {
            background: #28a745;
            color: white;
        }

        .step.completed::before {
            background: #28a745;
        }

        .step-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .step.active .step-label {
            color: #667eea;
            font-weight: 600;
        }

        /* Form Sections */
        .form-section {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .form-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* Scrollable content */
        .modal-body {
            max-height: 70vh;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #667eea #f0f0f0;
        }

        .modal-body::-webkit-scrollbar {
            width: 8px;
        }

        .modal-body::-webkit-scrollbar-track {
            background: #f0f0f0;
            border-radius: 10px;
        }

        .modal-body::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 10px;
        }

        .modal-body::-webkit-scrollbar-thumb:hover {
            background: #5a6fd8;
        }

        /* Smooth scroll behavior */
        .modal-body {
            scroll-behavior: smooth;
        }
        .close {
            color: rgba(255, 255, 255, 0.8);
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .close:hover {
            color: white;
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(90deg);
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .customer-dropdown {
            position: relative;
        }
        .customer-search {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
        }
        .customer-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #e0e0e0;
            border-top: none;
            border-radius: 0 0 10px 10px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 100;
            display: none;
        }
        .customer-result-item {
            padding: 12px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.3s ease;
        }
        .customer-result-item:hover {
            background: #f8f9fa;
        }
        .customer-result-item:last-child {
            border-bottom: none;
        }
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
            }
            .product-grid {
                grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>POS System</h1>
        <a href="dashboard.php" style="color: white; text-decoration: none;">← Dashboard</a>
    </div>

    <div class="container">
        <div class="products-section">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 style="color: #333; font-size: 1.5rem;">📦 Products (<?php echo count($products); ?>)</h3>
            </div>

            <!-- Product Search -->
            <input type="text" id="product-search" class="search-box" placeholder="🔍 Search products..." onkeyup="searchProducts()">

            <?php if (empty($products)): ?>
                <div style="text-align: center; padding: 40px; color: #666;">
                    <p style="font-size: 18px;">No products found</p>
                    <a href="../add_test_products.php" style="color: #667eea;">Add test products</a>
                </div>
            <?php else: ?>
                <div class="product-grid" id="product-grid">
                    <?php foreach ($products as $product): ?>
                        <div class="product-card" data-name="<?php echo strtolower($product['name']); ?>" onclick="addToCart(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars($product['name'], ENT_QUOTES); ?>', <?php echo $product['price']; ?>, <?php echo $product['stock_quantity']; ?>)">
                            <div class="product-image">
                                <?php
                                // Use ImageHandler for consistent image display
                                $imageHTML = ImageHandler::getImageHTML(
                                    $product['image'],
                                    $product['name'],
                                    '',
                                    'width: 100%; height: 100%; object-fit: cover; border-radius: 10px;',
                                    $product['id']
                                );
                                // Adjust path for POS system (add ../ prefix)
                                $imageHTML = str_replace('src="uploads/', 'src="../uploads/', $imageHTML);
                                $imageHTML = str_replace('data="uploads/', 'data="../uploads/', $imageHTML);
                                echo $imageHTML;
                                ?>
                            </div>
                            <div class="product-name"><?php echo htmlspecialchars($product['name']); ?></div>
                            <div class="product-price">৳<?php echo number_format($product['price'], 2); ?></div>
                            <div class="stock-info <?php echo $product['stock_quantity'] > 10 ? 'stock-available' : ($product['stock_quantity'] > 0 ? 'stock-low' : 'stock-out'); ?>">
                                <?php if ($product['stock_quantity'] > 0): ?>
                                    Stock: <?php echo $product['stock_quantity']; ?>
                                <?php else: ?>
                                    Out of Stock
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="cart-section">
            <h3>কার্ট</h3>
            <div id="cart-items">
                <p>কার্ট খালি</p>
            </div>

            <!-- Cart Summary -->
            <div style="margin: 15px 0; padding: 10px; background: #f0f0f0; border-radius: 5px;">
                <div>সাব টোটাল: ৳<span id="subtotal">0.00</span></div>
                <div>ছাড়: ৳<span id="discount-amount">0.00</span></div>
                <div style="font-weight: bold; font-size: 18px;">মোট: ৳<span id="cart-total">0.00</span></div>
            </div>

            <!-- Customer Information -->
            <div style="margin: 20px 0;">
                <h4 style="color: #333; margin-bottom: 15px;">👤 ক্রেতার তথ্য</h4>

                <!-- Customer Search with Dropdown -->
                <div class="customer-dropdown">
                    <input type="text" id="customer-search" class="customer-search" placeholder="🔍 ক্রেতা খুঁজুন বা নাম লিখুন..." onkeyup="searchCustomers()" onfocus="showCustomerOptions()">
                    <div id="customer-results" class="customer-results">
                        <div class="customer-result-item" onclick="selectWalkInCustomer()">
                            <strong>🚶 ওয়াক-ইন কাস্টমার</strong><br>
                            <small>অতিথি ক্রেতা</small>
                        </div>
                        <?php foreach ($customers as $customer): ?>
                            <div class="customer-result-item" onclick="selectExistingCustomer(<?php echo $customer['id']; ?>, '<?php echo htmlspecialchars($customer['name'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($customer['phone'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($customer['address'], ENT_QUOTES); ?>')">
                                <strong><?php echo htmlspecialchars($customer['name']); ?></strong><br>
                                <small>📞 <?php echo $customer['phone']; ?></small>
                            </div>
                        <?php endforeach; ?>
                        <div class="customer-result-item" onclick="openNewCustomerModal()" style="background: #f0f8ff; border-top: 2px solid #667eea;">
                            <strong style="color: #667eea;">➕ নতুন ক্রেতা যোগ করুন</strong><br>
                            <small style="color: #667eea;">নতুন ক্রেতা রেজিস্ট্রেশন</small>
                        </div>
                    </div>
                </div>

                <!-- Selected Customer Display -->
                <div id="selected-customer-info" style="display: none; background: linear-gradient(135deg, #d4edda, #c3e6cb); padding: 15px; margin: 15px 0; border-radius: 10px; border-left: 4px solid #28a745;">
                    <div style="display: flex; justify-content: space-between; align-items: start;">
                        <div>
                            <div style="font-weight: 600; color: #155724; margin-bottom: 5px;" id="display-name"></div>
                            <div style="color: #155724; font-size: 14px;" id="display-phone"></div>
                            <div style="color: #155724; font-size: 14px;" id="display-address"></div>
                        </div>
                        <button type="button" onclick="editCustomer()" class="btn-small" style="background: #ffc107; color: #333;">✏️ সম্পাদনা</button>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            <div style="margin: 15px 0;">
                <h4>পেমেন্ট তথ্য</h4>
                <select id="payment-method" style="width: 100%; padding: 8px; margin: 5px 0;">
                    <option value="">পেমেন্ট মেথড নির্বাচন করুন</option>
                    <option value="cash">নগদ</option>
                    <option value="card">কার্ড</option>
                    <option value="mobile_banking">মোবাইল ব্যাংকিং</option>
                    <option value="credit">বাকি</option>
                </select>

                <input type="number" id="discount-percent" placeholder="ছাড় (%)" style="width: 48%; padding: 8px; margin: 5px 1% 5px 0;" onchange="calculateDiscount()">
                <input type="number" id="paid-amount" placeholder="প্রদত্ত টাকা" style="width: 48%; padding: 8px; margin: 5px 0 5px 1%;" onchange="calculateChange()">

                <div style="background: #e8f5e8; padding: 10px; margin: 10px 0; border-radius: 5px;">
                    <div>ফেরত: ৳<span id="change-amount">0.00</span></div>
                    <div>বাকি: ৳<span id="due-amount">0.00</span></div>
                </div>
            </div>

            <!-- Commission Information -->
            <div style="margin: 15px 0; padding: 10px; background: #fff3cd; border-radius: 5px;">
                <h4>কমিশন তথ্য</h4>
                <div>সেলস কমিশন (5%): ৳<span id="commission-amount">0.00</span></div>
            </div>

            <button class="btn" onclick="checkout()" style="width: 100%; padding: 15px; font-size: 16px;">বিক্রয় সম্পন্ন করুন</button>
        </div>
    </div>

    <!-- New Customer Modal -->
    <div id="newCustomerModal" class="modal">
        <div class="modal-content" id="draggableModal">
            <div class="modal-header" id="modalHeader">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 20px;">⋮⋮</span>
                    <h3>➕ নতুন ক্রেতা যোগ করুন</h3>
                </div>
                <div class="modal-controls">
                    <span class="control-btn minimize-btn" id="minimizeBtn" onclick="toggleMinimizeModal()" title="Minimize">_</span>
                    <span class="control-btn maximize-btn" id="maximizeBtn" onclick="toggleMaximizeModal()" title="Maximize">□</span>
                    <span class="close" onclick="closeNewCustomerModal()" title="Close">&times;</span>
                </div>
            </div>
            <div class="modal-body">
                <!-- Form Steps Indicator -->
                <div class="form-steps">
                    <div class="step active" data-step="1">
                        <div class="step-circle">1</div>
                        <div class="step-label">ব্যক্তিগত তথ্য</div>
                    </div>
                    <div class="step" data-step="2">
                        <div class="step-circle">2</div>
                        <div class="step-label">যোগাযোগ</div>
                    </div>
                    <div class="step" data-step="3">
                        <div class="step-circle">3</div>
                        <div class="step-label">ঠিকানা</div>
                    </div>
                    <div class="step" data-step="4">
                        <div class="step-circle">4</div>
                        <div class="step-label">নিশ্চিতকরণ</div>
                    </div>
                </div>

                <form id="newCustomerForm">
                    <!-- Step 1: Personal Information -->
                    <div class="form-section active" data-section="1">
                        <h4 style="color: #333; margin-bottom: 20px; text-align: center;">👤 ব্যক্তিগত তথ্য</h4>
                        <div class="form-group">
                            <label for="new-customer-name">নাম *</label>
                            <input type="text" id="new-customer-name" class="form-control" required placeholder="ক্রেতার পূর্ণ নাম লিখুন" tabindex="1">
                            <small style="color: #666; font-size: 12px;">উদাহরণ: মোহাম্মদ রহিম</small>
                        </div>
                        <div class="form-group">
                            <label for="new-customer-gender">লিঙ্গ</label>
                            <select id="new-customer-gender" class="form-control" tabindex="2">
                                <option value="">নির্বাচন করুন</option>
                                <option value="male">পুরুষ</option>
                                <option value="female">মহিলা</option>
                                <option value="other">অন্যান্য</option>
                            </select>
                        </div>
                    </div>

                    <!-- Step 2: Contact Information -->
                    <div class="form-section" data-section="2">
                        <h4 style="color: #333; margin-bottom: 20px; text-align: center;">📞 যোগাযোগের তথ্য</h4>
                        <div class="form-group">
                            <label for="new-customer-phone">ফোন নম্বর *</label>
                            <input type="tel" id="new-customer-phone" class="form-control" placeholder="01XXXXXXXXX" required maxlength="11" pattern="01[3-9][0-9]{8}" tabindex="3">
                            <small style="color: #666; font-size: 12px;">উদাহরণ: 01712345678</small>
                        </div>
                        <div class="form-group">
                            <label for="new-customer-email">ইমেইল (ঐচ্ছিক)</label>
                            <input type="email" id="new-customer-email" class="form-control" placeholder="<EMAIL>" tabindex="4">
                            <small style="color: #666; font-size: 12px;">ইমেইল ঠিকানা (যদি থাকে)</small>
                        </div>
                        <div class="form-group">
                            <label for="new-customer-alt-phone">বিকল্প ফোন (ঐচ্ছিক)</label>
                            <input type="tel" id="new-customer-alt-phone" class="form-control" placeholder="01XXXXXXXXX" maxlength="11" tabindex="5">
                            <small style="color: #666; font-size: 12px;">জরুরি যোগাযোগের জন্য</small>
                        </div>
                    </div>

                    <!-- Step 3: Address Information -->
                    <div class="form-section" data-section="3">
                        <h4 style="color: #333; margin-bottom: 20px; text-align: center;">🏠 ঠিকানার তথ্য</h4>
                        <div class="form-group">
                            <label for="new-customer-address">ঠিকানা</label>
                            <textarea id="new-customer-address" class="form-control" rows="3" placeholder="বাড়ি/রোড নম্বর, এলাকা" tabindex="6"></textarea>
                            <small style="color: #666; font-size: 12px;">উদাহরণ: ১২৩/এ, ধানমন্ডি</small>
                        </div>
                        <div class="form-group">
                            <label for="new-customer-city">শহর</label>
                            <input type="text" id="new-customer-city" class="form-control" placeholder="শহরের নাম" tabindex="7">
                            <small style="color: #666; font-size: 12px;">উদাহরণ: ঢাকা</small>
                        </div>
                        <div class="form-group">
                            <label for="new-customer-postal">পোস্টাল কোড</label>
                            <input type="text" id="new-customer-postal" class="form-control" placeholder="1000" tabindex="8">
                            <small style="color: #666; font-size: 12px;">উদাহরণ: 1000</small>
                        </div>
                    </div>

                    <!-- Step 4: Confirmation -->
                    <div class="form-section" data-section="4">
                        <h4 style="color: #333; margin-bottom: 20px; text-align: center;">✅ তথ্য নিশ্চিতকরণ</h4>
                        <div id="customer-preview" style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                            <!-- Customer preview will be populated by JavaScript -->
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="terms-agreement" required tabindex="9">
                                আমি নিশ্চিত করছি যে উপরের তথ্যগুলি সঠিক এবং আমি শর্তাবলীর সাথে একমত।
                            </label>
                        </div>
                    </div>

                <!-- Error message display -->
                <div id="customer-error" style="display: none; background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #dc3545;">
                    <span id="customer-error-text"></span>
                </div>

                <!-- Success message display -->
                <div id="customer-success" style="display: none; background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #28a745;">
                    <span id="customer-success-text"></span>
                </div>

                <!-- Form Navigation -->
                <div class="form-navigation">
                    <div class="nav-info">
                        <span id="step-info">ধাপ 1 এর 4</span>
                    </div>
                    <div class="nav-buttons">
                        <button type="button" class="nav-btn nav-btn-prev" id="prev-btn" onclick="previousStep()" disabled>
                            ← পূর্ববর্তী
                        </button>
                        <button type="button" class="nav-btn nav-btn-next" id="next-btn" onclick="nextStep()">
                            পরবর্তী →
                        </button>
                        <button type="button" onclick="closeNewCustomerModal()" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 20px; cursor: pointer; transition: all 0.3s ease;">❌ বাতিল</button>
                        <button type="submit" class="btn btn-primary" id="save-customer-btn" style="display: none;">💾 সংরক্ষণ করুন</button>
                    </div>
                </div>
                </form>
            </div>

            <!-- Resize Handles -->
            <div class="resize-handle resize-handle-n" data-direction="n"></div>
            <div class="resize-handle resize-handle-s" data-direction="s"></div>
            <div class="resize-handle resize-handle-e" data-direction="e"></div>
            <div class="resize-handle resize-handle-w" data-direction="w"></div>
            <div class="resize-handle resize-handle-ne" data-direction="ne"></div>
            <div class="resize-handle resize-handle-nw" data-direction="nw"></div>
            <div class="resize-handle resize-handle-se" data-direction="se"></div>
            <div class="resize-handle resize-handle-sw" data-direction="sw"></div>

            <!-- Resize Indicator -->
            <div class="resize-indicator"></div>
        </div>
    </div>

    <script>
        let cart = [];
        let selectedCustomerData = null;

        // Product search function
        function searchProducts() {
            const searchTerm = document.getElementById('product-search').value.toLowerCase();
            const productCards = document.querySelectorAll('.product-card');

            productCards.forEach(card => {
                const productName = card.getAttribute('data-name');
                if (productName.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Customer management functions
        function showCustomerOptions() {
            document.getElementById('customer-results').style.display = 'block';
        }

        function hideCustomerOptions() {
            setTimeout(() => {
                document.getElementById('customer-results').style.display = 'none';
            }, 200);
        }

        function searchCustomers() {
            const searchTerm = document.getElementById('customer-search').value.toLowerCase();
            const customerItems = document.querySelectorAll('.customer-result-item');

            customerItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });

            showCustomerOptions();
        }

        function selectWalkInCustomer() {
            document.getElementById('customer-search').value = 'ওয়াক-ইন কাস্টমার';
            document.getElementById('selected-customer-info').style.display = 'block';
            document.getElementById('display-name').textContent = '🚶 ওয়াক-ইন কাস্টমার';
            document.getElementById('display-phone').textContent = '';
            document.getElementById('display-address').textContent = '';
            selectedCustomerData = {
                id: null,
                name: 'ওয়াক-ইন কাস্টমার',
                phone: '',
                address: ''
            };
            hideCustomerOptions();
        }

        function selectExistingCustomer(id, name, phone, address) {
            document.getElementById('customer-search').value = name;
            document.getElementById('selected-customer-info').style.display = 'block';
            document.getElementById('display-name').textContent = '👤 ' + name;
            document.getElementById('display-phone').textContent = '📞 ' + phone;
            document.getElementById('display-address').textContent = '📍 ' + address;
            selectedCustomerData = {
                id: id,
                name: name,
                phone: phone,
                address: address
            };
            hideCustomerOptions();
        }

        function openNewCustomerModal() {
            resetModalPosition();
            document.getElementById('newCustomerModal').style.display = 'block';
            hideCustomerOptions();

            // Initialize form navigation
            initializeFormNavigation();

            // Add a subtle animation
            const modal = document.getElementById('draggableModal');
            modal.style.opacity = '0';
            modal.style.transform = 'scale(0.8)';

            setTimeout(() => {
                modal.style.transition = 'all 0.3s ease';
                modal.style.opacity = '1';
                modal.style.transform = 'scale(1)';

                // Focus on first input
                document.getElementById('new-customer-name').focus();
            }, 10);
        }

        function closeNewCustomerModal() {
            const modal = document.getElementById('draggableModal');
            modal.style.transition = 'all 0.3s ease';
            modal.style.opacity = '0';
            modal.style.transform = 'scale(0.8)';

            setTimeout(() => {
                document.getElementById('newCustomerModal').style.display = 'none';
                document.getElementById('newCustomerForm').reset();
                hideCustomerMessages();
                modal.style.transition = '';
            }, 300);
        }

        function showCustomerError(message) {
            document.getElementById('customer-error-text').textContent = message;
            document.getElementById('customer-error').style.display = 'block';
            document.getElementById('customer-success').style.display = 'none';
        }

        function showCustomerSuccess(message) {
            document.getElementById('customer-success-text').textContent = message;
            document.getElementById('customer-success').style.display = 'block';
            document.getElementById('customer-error').style.display = 'none';
        }

        function hideCustomerMessages() {
            document.getElementById('customer-error').style.display = 'none';
            document.getElementById('customer-success').style.display = 'none';
        }

        function validateEmail(email) {
            const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(String(email).toLowerCase());
        }

        // Form navigation variables
        let currentStep = 1;
        const totalSteps = 4;

        // Form navigation functions
        function nextStep() {
            if (currentStep < totalSteps) {
                if (validateCurrentStep()) {
                    currentStep++;
                    showStep(currentStep);
                    updateStepIndicators();
                    updateNavigationButtons();

                    if (currentStep === 4) {
                        updateCustomerPreview();
                    }
                }
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
                updateStepIndicators();
                updateNavigationButtons();
            }
        }

        function showStep(step) {
            // Hide all sections
            document.querySelectorAll('.form-section').forEach(section => {
                section.classList.remove('active');
            });

            // Show current section
            document.querySelector(`[data-section="${step}"]`).classList.add('active');

            // Focus on first input of current step
            const firstInput = document.querySelector(`[data-section="${step}"] input, [data-section="${step}"] select, [data-section="${step}"] textarea`);
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }

            // Scroll to top of modal body
            document.querySelector('.modal-body').scrollTop = 0;
        }

        function updateStepIndicators() {
            document.querySelectorAll('.step').forEach((step, index) => {
                const stepNumber = index + 1;
                step.classList.remove('active', 'completed');

                if (stepNumber === currentStep) {
                    step.classList.add('active');
                } else if (stepNumber < currentStep) {
                    step.classList.add('completed');
                }
            });

            document.getElementById('step-info').textContent = `ধাপ ${currentStep} এর ${totalSteps}`;
        }

        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            const saveBtn = document.getElementById('save-customer-btn');

            prevBtn.disabled = currentStep === 1;

            if (currentStep === totalSteps) {
                nextBtn.style.display = 'none';
                saveBtn.style.display = 'inline-block';
            } else {
                nextBtn.style.display = 'inline-block';
                saveBtn.style.display = 'none';
            }
        }

        function validateCurrentStep() {
            hideCustomerMessages();

            switch (currentStep) {
                case 1:
                    const name = document.getElementById('new-customer-name').value.trim();
                    if (!name) {
                        showCustomerError('নাম আবশ্যক!');
                        document.getElementById('new-customer-name').focus();
                        return false;
                    }
                    break;

                case 2:
                    const phone = document.getElementById('new-customer-phone').value.trim();
                    if (!phone) {
                        showCustomerError('ফোন নম্বর আবশ্যক!');
                        document.getElementById('new-customer-phone').focus();
                        return false;
                    }
                    if (!/^01[3-9]\d{8}$/.test(phone)) {
                        showCustomerError('সঠিক ফোন নম্বর দিন (01XXXXXXXXX)!');
                        document.getElementById('new-customer-phone').focus();
                        return false;
                    }

                    const email = document.getElementById('new-customer-email').value.trim();
                    if (email && !validateEmail(email)) {
                        showCustomerError('সঠিক ইমেইল ঠিকানা দিন!');
                        document.getElementById('new-customer-email').focus();
                        return false;
                    }
                    break;

                case 3:
                    // Address validation (optional)
                    break;

                case 4:
                    const termsAgreed = document.getElementById('terms-agreement').checked;
                    if (!termsAgreed) {
                        showCustomerError('শর্তাবলীতে সম্মতি দিন!');
                        return false;
                    }
                    break;
            }

            return true;
        }

        function updateCustomerPreview() {
            const preview = document.getElementById('customer-preview');
            const name = document.getElementById('new-customer-name').value.trim();
            const gender = document.getElementById('new-customer-gender').value;
            const phone = document.getElementById('new-customer-phone').value.trim();
            const email = document.getElementById('new-customer-email').value.trim();
            const altPhone = document.getElementById('new-customer-alt-phone').value.trim();
            const address = document.getElementById('new-customer-address').value.trim();
            const city = document.getElementById('new-customer-city').value.trim();
            const postal = document.getElementById('new-customer-postal').value.trim();

            let previewHTML = '<h5 style="color: #333; margin-bottom: 15px;">📋 তথ্য পর্যালোচনা</h5>';

            previewHTML += `<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">`;
            previewHTML += `<div><strong>নাম:</strong> ${name || 'N/A'}</div>`;
            previewHTML += `<div><strong>লিঙ্গ:</strong> ${gender || 'N/A'}</div>`;
            previewHTML += `<div><strong>ফোন:</strong> ${phone || 'N/A'}</div>`;
            previewHTML += `<div><strong>ইমেইল:</strong> ${email || 'N/A'}</div>`;
            previewHTML += `<div><strong>বিকল্প ফোন:</strong> ${altPhone || 'N/A'}</div>`;
            previewHTML += `<div><strong>শহর:</strong> ${city || 'N/A'}</div>`;
            previewHTML += `</div>`;

            if (address) {
                previewHTML += `<div style="margin-top: 15px;"><strong>ঠিকানা:</strong> ${address}</div>`;
            }

            if (postal) {
                previewHTML += `<div style="margin-top: 10px;"><strong>পোস্টাল কোড:</strong> ${postal}</div>`;
            }

            preview.innerHTML = previewHTML;
        }

        function editCustomer() {
            if (selectedCustomerData && selectedCustomerData.id) {
                // Open modal with existing data
                document.getElementById('new-customer-name').value = selectedCustomerData.name;
                document.getElementById('new-customer-phone').value = selectedCustomerData.phone;
                document.getElementById('new-customer-address').value = selectedCustomerData.address;
                openNewCustomerModal();
            }
        }

        function getCustomerData() {
            if (selectedCustomerData) {
                return selectedCustomerData;
            } else {
                return {
                    id: null,
                    name: document.getElementById('customer-search').value.trim(),
                    phone: '',
                    address: ''
                };
            }
        }

        // Initialize form navigation when modal opens
        function initializeFormNavigation() {
            currentStep = 1;
            showStep(currentStep);
            updateStepIndicators();
            updateNavigationButtons();
        }

        // New customer form submission
        document.getElementById('newCustomerForm').addEventListener('submit', function(e) {
            e.preventDefault();

            if (!validateCurrentStep()) {
                return;
            }

            const name = document.getElementById('new-customer-name').value.trim();
            const gender = document.getElementById('new-customer-gender').value;
            const phone = document.getElementById('new-customer-phone').value.trim();
            const email = document.getElementById('new-customer-email').value.trim();
            const altPhone = document.getElementById('new-customer-alt-phone').value.trim();
            const address = document.getElementById('new-customer-address').value.trim();
            const city = document.getElementById('new-customer-city').value.trim();
            const postal = document.getElementById('new-customer-postal').value.trim();

            // Show loading state
            const submitBtn = document.querySelector('#newCustomerForm button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '⏳ সংরক্ষণ করা হচ্ছে...';
            submitBtn.disabled = true;

            // Prepare data
            const customerData = {
                name: name,
                gender: gender,
                phone: phone,
                email: email,
                alt_phone: altPhone,
                address: address,
                city: city,
                postal_code: postal
            };

            // Send to server
            fetch('api/add_customer.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(customerData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showCustomerSuccess(data.message);

                    // Add to customer dropdown
                    addCustomerToDropdown(data.data);

                    // Select the new customer
                    selectExistingCustomer(data.data.id, data.data.name, data.data.phone, data.data.address);

                    // Close modal after a short delay
                    setTimeout(() => {
                        closeNewCustomerModal();
                    }, 1500);
                } else {
                    showCustomerError(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showCustomerError('নেটওয়ার্ক এরর! আবার চেষ্টা করুন।');
            })
            .finally(() => {
                // Reset button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Function to add new customer to dropdown
        function addCustomerToDropdown(customer) {
            const customerResults = document.getElementById('customer-results');
            const newCustomerOption = document.querySelector('.customer-result-item[onclick*="openNewCustomerModal"]');

            // Create new customer item
            const newItem = document.createElement('div');
            newItem.className = 'customer-result-item';
            newItem.onclick = function() {
                selectExistingCustomer(customer.id, customer.name, customer.phone, customer.address);
            };
            newItem.innerHTML = `
                <strong>${customer.name}</strong><br>
                <small>📞 ${customer.phone}</small>
            `;

            // Insert before the "Add new customer" option
            customerResults.insertBefore(newItem, newCustomerOption);
        }

        // Modal drag and resize functionality
        let isDragging = false;
        let isResizing = false;
        let resizeDirection = '';
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;
        let startWidth, startHeight, startX, startY;

        function initializeDragModal() {
            const modal = document.getElementById('draggableModal');
            const header = document.getElementById('modalHeader');
            const resizeHandles = modal.querySelectorAll('.resize-handle');
            const resizeIndicator = modal.querySelector('.resize-indicator');

            // Drag events
            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', dragAndResize);
            document.addEventListener('mouseup', dragAndResizeEnd);

            // Touch events for mobile
            header.addEventListener('touchstart', dragStart);
            document.addEventListener('touchmove', dragAndResize);
            document.addEventListener('touchend', dragAndResizeEnd);

            // Resize events
            resizeHandles.forEach(handle => {
                handle.addEventListener('mousedown', resizeStart);
                handle.addEventListener('touchstart', resizeStart);
            });

            // Resize indicator
            resizeIndicator.addEventListener('mousedown', resizeStart);
            resizeIndicator.addEventListener('touchstart', resizeStart);
        }

        function resizeStart(e) {
            e.preventDefault();
            e.stopPropagation();

            // Don't resize if maximized
            if (isMaximized) return;

            const modal = document.getElementById('draggableModal');
            const rect = modal.getBoundingClientRect();

            isResizing = true;
            resizeDirection = e.target.dataset.direction || 'se';

            startWidth = rect.width;
            startHeight = rect.height;
            startX = rect.left;
            startY = rect.top;

            if (e.type === "touchstart") {
                initialX = e.touches[0].clientX;
                initialY = e.touches[0].clientY;
            } else {
                initialX = e.clientX;
                initialY = e.clientY;
            }

            // Set position to fixed for resizing
            modal.style.position = 'fixed';
            modal.style.margin = '0';
            modal.style.top = startY + 'px';
            modal.style.left = startX + 'px';
            modal.style.width = startWidth + 'px';
            modal.style.height = startHeight + 'px';
        }

        function dragStart(e) {
            const modal = document.getElementById('draggableModal');

            // Don't drag if maximized or resizing
            if (isMaximized || isResizing) return;

            if (e.type === "touchstart") {
                initialX = e.touches[0].clientX - xOffset;
                initialY = e.touches[0].clientY - yOffset;
            } else {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;
            }

            const header = document.getElementById('modalHeader');
            const target = e.target;

            // Only drag if clicking on header but not on control buttons
            if ((target === header || header.contains(target)) &&
                !target.classList.contains('control-btn') &&
                !target.classList.contains('close') &&
                !target.classList.contains('resize-handle')) {
                isDragging = true;
                modal.style.position = 'fixed';
                modal.style.margin = '0';
                modal.style.top = modal.offsetTop + 'px';
                modal.style.left = modal.offsetLeft + 'px';
            }
        }

        function dragAndResize(e) {
            if (isDragging) {
                e.preventDefault();
                const modal = document.getElementById('draggableModal');

                if (e.type === "touchmove") {
                    currentX = e.touches[0].clientX - initialX;
                    currentY = e.touches[0].clientY - initialY;
                } else {
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;
                }

                xOffset = currentX;
                yOffset = currentY;

                // Boundary checks
                const rect = modal.getBoundingClientRect();
                const maxX = window.innerWidth - rect.width;
                const maxY = window.innerHeight - rect.height;

                if (currentX < 0) currentX = 0;
                if (currentY < 0) currentY = 0;
                if (currentX > maxX) currentX = maxX;
                if (currentY > maxY) currentY = maxY;

                modal.style.transform = `translate(${currentX}px, ${currentY}px)`;
            } else if (isResizing) {
                e.preventDefault();
                handleResize(e);
            }
        }

        function handleResize(e) {
            const modal = document.getElementById('draggableModal');

            let clientX, clientY;
            if (e.type === "touchmove") {
                clientX = e.touches[0].clientX;
                clientY = e.touches[0].clientY;
            } else {
                clientX = e.clientX;
                clientY = e.clientY;
            }

            const deltaX = clientX - initialX;
            const deltaY = clientY - initialY;

            let newWidth = startWidth;
            let newHeight = startHeight;
            let newX = startX;
            let newY = startY;

            // Calculate new dimensions based on resize direction
            if (resizeDirection.includes('e')) {
                newWidth = Math.max(400, startWidth + deltaX);
            }
            if (resizeDirection.includes('w')) {
                newWidth = Math.max(400, startWidth - deltaX);
                newX = startX + (startWidth - newWidth);
            }
            if (resizeDirection.includes('s')) {
                newHeight = Math.max(300, startHeight + deltaY);
            }
            if (resizeDirection.includes('n')) {
                newHeight = Math.max(300, startHeight - deltaY);
                newY = startY + (startHeight - newHeight);
            }

            // Boundary checks
            if (newX < 0) {
                newWidth += newX;
                newX = 0;
            }
            if (newY < 0) {
                newHeight += newY;
                newY = 0;
            }
            if (newX + newWidth > window.innerWidth) {
                newWidth = window.innerWidth - newX;
            }
            if (newY + newHeight > window.innerHeight) {
                newHeight = window.innerHeight - newY;
            }

            // Apply new dimensions
            modal.style.width = newWidth + 'px';
            modal.style.height = newHeight + 'px';
            modal.style.left = newX + 'px';
            modal.style.top = newY + 'px';

            // Adjust modal body height
            const modalBody = modal.querySelector('.modal-body');
            const headerHeight = modal.querySelector('.modal-header').offsetHeight;
            modalBody.style.height = (newHeight - headerHeight - 20) + 'px';
            modalBody.style.overflowY = 'auto';
        }

        function dragAndResizeEnd(e) {
            if (isDragging) {
                initialX = currentX;
                initialY = currentY;
                isDragging = false;
            }
            if (isResizing) {
                isResizing = false;
                resizeDirection = '';
            }
        }

        // Modal state variables
        let isMinimized = false;
        let isMaximized = false;
        let originalModalStyle = {};

        // Reset modal position when opening
        function resetModalPosition() {
            const modal = document.getElementById('draggableModal');
            const modalBody = modal.querySelector('.modal-body');

            modal.style.position = 'relative';
            modal.style.margin = '5% auto';
            modal.style.top = 'auto';
            modal.style.left = 'auto';
            modal.style.width = '90%';
            modal.style.maxWidth = '500px';
            modal.style.height = 'auto';
            modal.style.transform = 'none';
            modal.classList.remove('modal-minimized');

            // Reset modal body
            modalBody.style.height = 'auto';
            modalBody.style.overflowY = 'visible';

            xOffset = 0;
            yOffset = 0;
            currentX = 0;
            currentY = 0;
            isMinimized = false;
            isMaximized = false;
            isResizing = false;

            // Reset button states
            updateControlButtons();
        }

        // Toggle minimize modal
        function toggleMinimizeModal() {
            const modal = document.getElementById('draggableModal');

            if (isMinimized) {
                // Restore modal
                modal.classList.remove('modal-minimized');
                isMinimized = false;
            } else {
                // Minimize modal
                modal.classList.add('modal-minimized');
                isMinimized = true;

                // If maximized, restore first
                if (isMaximized) {
                    restoreModalSize();
                    isMaximized = false;
                }
            }

            updateControlButtons();
        }

        // Toggle maximize modal
        function toggleMaximizeModal() {
            const modal = document.getElementById('draggableModal');

            if (isMaximized) {
                // Restore modal
                restoreModalSize();
                isMaximized = false;
            } else {
                // Save current style
                saveModalStyle();

                // Maximize modal
                modal.style.position = 'fixed';
                modal.style.top = '20px';
                modal.style.left = '20px';
                modal.style.width = 'calc(100vw - 40px)';
                modal.style.height = 'calc(100vh - 40px)';
                modal.style.maxWidth = 'none';
                modal.style.margin = '0';
                modal.style.transform = 'none';

                // Adjust body height
                const modalBody = modal.querySelector('.modal-body');
                modalBody.style.height = 'calc(100% - 80px)';
                modalBody.style.overflowY = 'auto';

                isMaximized = true;

                // If minimized, restore first
                if (isMinimized) {
                    modal.classList.remove('modal-minimized');
                    isMinimized = false;
                }
            }

            updateControlButtons();
        }

        // Save current modal style
        function saveModalStyle() {
            const modal = document.getElementById('draggableModal');
            originalModalStyle = {
                position: modal.style.position,
                top: modal.style.top,
                left: modal.style.left,
                width: modal.style.width,
                height: modal.style.height,
                maxWidth: modal.style.maxWidth,
                margin: modal.style.margin,
                transform: modal.style.transform
            };
        }

        // Restore modal size
        function restoreModalSize() {
            const modal = document.getElementById('draggableModal');
            const modalBody = modal.querySelector('.modal-body');

            // Restore original style
            Object.keys(originalModalStyle).forEach(key => {
                modal.style[key] = originalModalStyle[key];
            });

            // Reset body style
            modalBody.style.height = 'auto';
            modalBody.style.overflowY = 'visible';
        }

        // Update control button appearance
        function updateControlButtons() {
            const minimizeBtn = document.getElementById('minimizeBtn');
            const maximizeBtn = document.getElementById('maximizeBtn');

            // Update minimize button
            if (isMinimized) {
                minimizeBtn.innerHTML = '▲';
                minimizeBtn.title = 'Restore';
            } else {
                minimizeBtn.innerHTML = '_';
                minimizeBtn.title = 'Minimize';
            }

            // Update maximize button
            if (isMaximized) {
                maximizeBtn.innerHTML = '❐';
                maximizeBtn.title = 'Restore';
            } else {
                maximizeBtn.innerHTML = '□';
                maximizeBtn.title = 'Maximize';
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('newCustomerModal');
            if (event.target === modal) {
                closeNewCustomerModal();
            }
        }

        // Keyboard shortcuts for modal
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('newCustomerModal');
            if (modal.style.display === 'block') {
                if (e.key === 'Escape') {
                    closeNewCustomerModal();
                } else if (e.key === 'F11' || (e.ctrlKey && e.key === 'm')) {
                    e.preventDefault();
                    toggleMaximizeModal();
                } else if (e.ctrlKey && e.key === '-') {
                    e.preventDefault();
                    toggleMinimizeModal();
                } else if (e.key === 'Enter' && e.ctrlKey) {
                    // Ctrl+Enter to submit form
                    e.preventDefault();
                    if (currentStep === totalSteps) {
                        document.getElementById('save-customer-btn').click();
                    } else {
                        nextStep();
                    }
                } else if (e.key === 'ArrowRight' && e.altKey) {
                    // Alt+Right Arrow for next step
                    e.preventDefault();
                    nextStep();
                } else if (e.key === 'ArrowLeft' && e.altKey) {
                    // Alt+Left Arrow for previous step
                    e.preventDefault();
                    previousStep();
                } else if (e.key >= '1' && e.key <= '4' && e.ctrlKey) {
                    // Ctrl+1, Ctrl+2, etc. to jump to specific step
                    e.preventDefault();
                    const targetStep = parseInt(e.key);
                    if (targetStep <= currentStep || validateCurrentStep()) {
                        currentStep = targetStep;
                        showStep(currentStep);
                        updateStepIndicators();
                        updateNavigationButtons();

                        if (currentStep === 4) {
                            updateCustomerPreview();
                        }
                    }
                }
            }
        });

        // Initialize drag functionality when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeDragModal();
        });

        // Hide customer options when clicking outside
        document.addEventListener('click', function(event) {
            const customerDropdown = document.querySelector('.customer-dropdown');
            if (!customerDropdown.contains(event.target)) {
                hideCustomerOptions();
            }
        });

        function addToCart(id, name, price, stock) {
            if (stock <= 0) {
                alert('Out of stock!');
                return;
            }

            // Check if item already in cart
            let existingItem = cart.find(item => item.id === id);
            if (existingItem) {
                if (existingItem.quantity >= stock) {
                    alert('Not enough stock!');
                    return;
                }
                existingItem.quantity++;
            } else {
                cart.push({
                    id: id,
                    name: name,
                    price: price,
                    quantity: 1,
                    stock: stock
                });
            }

            updateCartDisplay();
        }

        function updateQuantity(id, newQuantity) {
            let item = cart.find(item => item.id === id);
            if (item) {
                if (newQuantity <= 0) {
                    cart = cart.filter(item => item.id !== id);
                } else if (newQuantity <= item.stock) {
                    item.quantity = newQuantity;
                } else {
                    alert('Not enough stock!');
                    return;
                }
                updateCartDisplay();
            }
        }

        function updateCartDisplay() {
            let cartDiv = document.getElementById('cart-items');
            let subtotalSpan = document.getElementById('subtotal');
            let totalSpan = document.getElementById('cart-total');

            if (cart.length === 0) {
                cartDiv.innerHTML = '<p>কার্ট খালি</p>';
                subtotalSpan.textContent = '0.00';
                totalSpan.textContent = '0.00';
                updateCommission();
                return;
            }

            let html = '';
            let subtotal = 0;

            cart.forEach(item => {
                let itemTotal = item.price * item.quantity;
                subtotal += itemTotal;

                html += `
                    <div class="cart-item">
                        <div><strong>${item.name}</strong></div>
                        <div>
                            <button class="btn btn-small" onclick="updateQuantity(${item.id}, ${item.quantity - 1})">-</button>
                            ${item.quantity}
                            <button class="btn btn-small" onclick="updateQuantity(${item.id}, ${item.quantity + 1})">+</button>
                            <span style="float: right;">৳${itemTotal.toFixed(2)}</span>
                        </div>
                    </div>
                `;
            });

            cartDiv.innerHTML = html;
            subtotalSpan.textContent = subtotal.toFixed(2);

            calculateDiscount();
            updateCommission();
        }

        function calculateDiscount() {
            let subtotal = parseFloat(document.getElementById('subtotal').textContent);
            let discountPercent = parseFloat(document.getElementById('discount-percent').value) || 0;
            let discountAmount = (subtotal * discountPercent) / 100;
            let total = subtotal - discountAmount;

            document.getElementById('discount-amount').textContent = discountAmount.toFixed(2);
            document.getElementById('cart-total').textContent = total.toFixed(2);

            calculateChange();
            updateCommission();
        }

        function calculateChange() {
            let total = parseFloat(document.getElementById('cart-total').textContent);
            let paidAmount = parseFloat(document.getElementById('paid-amount').value) || 0;
            let changeAmount = paidAmount - total;
            let dueAmount = total - paidAmount;

            document.getElementById('change-amount').textContent = changeAmount > 0 ? changeAmount.toFixed(2) : '0.00';
            document.getElementById('due-amount').textContent = dueAmount > 0 ? dueAmount.toFixed(2) : '0.00';
        }

        function updateCommission() {
            let total = parseFloat(document.getElementById('cart-total').textContent);
            let commission = total * 0.05; // 5% commission
            document.getElementById('commission-amount').textContent = commission.toFixed(2);
        }

        function checkout() {
            if (cart.length === 0) {
                alert('কার্ট খালি!');
                return;
            }

            // Get customer data
            let customerData = getCustomerData();
            let paymentMethod = document.getElementById('payment-method').value;

            if (!customerData.name) {
                alert('ক্রেতার নাম আবশ্যক!');
                return;
            }

            if (!paymentMethod) {
                alert('পেমেন্ট মেথড নির্বাচন করুন!');
                return;
            }

            // Get all sale data
            let saleData = {
                customer: customerData,
                payment: {
                    method: paymentMethod,
                    subtotal: parseFloat(document.getElementById('subtotal').textContent),
                    discount: parseFloat(document.getElementById('discount-amount').textContent),
                    total: parseFloat(document.getElementById('cart-total').textContent),
                    paid: parseFloat(document.getElementById('paid-amount').value) || 0,
                    change: parseFloat(document.getElementById('change-amount').textContent),
                    due: parseFloat(document.getElementById('due-amount').textContent)
                },
                commission: parseFloat(document.getElementById('commission-amount').textContent),
                items: cart
            };

            // Show sale summary
            let summary = `বিক্রয় সারসংক্ষেপ:\n\n`;
            summary += `ক্রেতা: ${saleData.customer.name}\n`;
            summary += `ফোন: ${saleData.customer.phone}\n`;
            summary += `মোট: ৳${saleData.payment.total.toFixed(2)}\n`;
            summary += `প্রদত্ত: ৳${saleData.payment.paid.toFixed(2)}\n`;
            summary += `ফেরত: ৳${saleData.payment.change.toFixed(2)}\n`;
            summary += `বাকি: ৳${saleData.payment.due.toFixed(2)}\n`;
            summary += `কমিশন: ৳${saleData.commission.toFixed(2)}\n\n`;
            summary += `বিক্রয় সম্পন্ন করবেন?`;

            if (confirm(summary)) {
                // Here you would normally send data to server
                alert('বিক্রয় সফলভাবে সম্পন্ন হয়েছে!');

                // Reset form
                cart = [];
                document.getElementById('customer-search').value = '';
                document.getElementById('selected-customer-info').style.display = 'none';
                selectedCustomerData = null;
                document.getElementById('payment-method').value = '';
                document.getElementById('discount-percent').value = '';
                document.getElementById('paid-amount').value = '';

                updateCartDisplay();
            }
        }
    </script>
</body>
</html>
