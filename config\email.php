<?php
// Email Configuration and Functions

// Email settings (configure these for production)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'আমার দোকান');

/**
 * Send password reset email
 * In production, this would send actual email
 * For demo, it returns the email content
 */
function send_password_reset_email($customer, $reset_token) {
    $reset_link = SITE_URL . "/forgot_password.php?step=reset&token=" . $reset_token;
    
    $subject = "পাসওয়ার্ড রিসেট - " . SITE_NAME;
    
    $message = "
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: 'Arial', sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>পাসওয়ার্ড রিসেট</h1>
                <p>" . SITE_NAME . "</p>
            </div>
            <div class='content'>
                <h2>প্রিয় " . htmlspecialchars($customer['name']) . ",</h2>
                
                <p>আপনি আপনার অ্যাকাউন্টের পাসওয়ার্ড রিসেট করার অনুরোধ করেছেন।</p>
                
                <p>নিচের বাটনে ক্লিক করে আপনার নতুন পাসওয়ার্ড সেট করুন:</p>
                
                <div style='text-align: center;'>
                    <a href='" . $reset_link . "' class='button'>পাসওয়ার্ড রিসেট করুন</a>
                </div>
                
                <p>অথবা এই লিংকটি কপি করে ব্রাউজারে পেস্ট করুন:</p>
                <p style='word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 5px;'>" . $reset_link . "</p>
                
                <div class='warning'>
                    <strong>গুরুত্বপূর্ণ তথ্য:</strong>
                    <ul>
                        <li>এই লিংকটি ১ ঘন্টার জন্য বৈধ</li>
                        <li>নিরাপত্তার জন্য, লিংকটি শুধুমাত্র একবার ব্যবহার করা যাবে</li>
                        <li>যদি আপনি এই অনুরোধ না করে থাকেন, তাহলে এই ইমেইলটি উপেক্ষা করুন</li>
                    </ul>
                </div>
                
                <p>ধন্যবাদ,<br>" . SITE_NAME . " টিম</p>
            </div>
            <div class='footer'>
                <p>এই ইমেইলটি স্বয়ংক্রিয়ভাবে পাঠানো হয়েছে। অনুগ্রহ করে উত্তর দেবেন না।</p>
                <p>&copy; " . date('Y') . " " . SITE_NAME . ". সকল অধিকার সংরক্ষিত।</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    // For demo purposes, return the email content instead of sending
    // In production, you would use PHPMailer or similar to send actual email
    
    return [
        'success' => true,
        'message' => 'ইমেইল পাঠানো হয়েছে',
        'demo_content' => [
            'to' => $customer['email'] ?: $customer['phone'],
            'subject' => $subject,
            'body' => $message,
            'reset_link' => $reset_link
        ]
    ];
}

/**
 * Send SMS for password reset (for customers without email)
 * In production, this would send actual SMS
 */
function send_password_reset_sms($customer, $reset_token) {
    $reset_link = SITE_URL . "/forgot_password.php?step=reset&token=" . $reset_token;
    
    $message = "আপনার " . SITE_NAME . " অ্যাকাউন্টের পাসওয়ার্ড রিসেট করুন: " . $reset_link . " (১ ঘন্টার জন্য বৈধ)";
    
    // For demo purposes, return the SMS content
    // In production, you would use SMS gateway API
    
    return [
        'success' => true,
        'message' => 'SMS পাঠানো হয়েছে',
        'demo_content' => [
            'to' => $customer['phone'],
            'message' => $message,
            'reset_link' => $reset_link
        ]
    ];
}

/**
 * Production email sending function using PHPMailer
 * Uncomment and configure for production use
 */
/*
function send_email_production($to, $subject, $body, $altBody = '') {
    require_once 'vendor/autoload.php'; // PHPMailer autoload
    
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host       = SMTP_HOST;
        $mail->SMTPAuth   = true;
        $mail->Username   = SMTP_USERNAME;
        $mail->Password   = SMTP_PASSWORD;
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = SMTP_PORT;
        $mail->CharSet    = 'UTF-8';
        
        // Recipients
        $mail->setFrom(FROM_EMAIL, FROM_NAME);
        $mail->addAddress($to);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body    = $body;
        $mail->AltBody = $altBody;
        
        $mail->send();
        return ['success' => true, 'message' => 'Email sent successfully'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => "Email could not be sent. Error: {$mail->ErrorInfo}"];
    }
}
*/

/**
 * Clean up expired password reset tokens
 * Call this periodically to maintain database
 */
function cleanup_expired_tokens() {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("DELETE FROM password_reset_tokens WHERE expires_at < NOW() OR used = TRUE");
        $stmt->execute();
        
        return ['success' => true, 'message' => 'Expired tokens cleaned up'];
    } catch(PDOException $e) {
        return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Get password reset statistics
 * For admin dashboard
 */
function get_password_reset_stats() {
    global $pdo;
    
    try {
        $stats = [];
        
        // Total reset requests today
        $stmt = $pdo->query("SELECT COUNT(*) FROM password_reset_tokens WHERE DATE(created_at) = CURDATE()");
        $stats['today'] = $stmt->fetchColumn();
        
        // Total reset requests this week
        $stmt = $pdo->query("SELECT COUNT(*) FROM password_reset_tokens WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stats['week'] = $stmt->fetchColumn();
        
        // Successful resets today
        $stmt = $pdo->query("SELECT COUNT(*) FROM password_reset_tokens WHERE used = TRUE AND DATE(created_at) = CURDATE()");
        $stats['successful_today'] = $stmt->fetchColumn();
        
        return ['success' => true, 'stats' => $stats];
    } catch(PDOException $e) {
        return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
    }
}
?>
