<?php
require_once '../config/database.php';
require_once '../config/functions.php';
require_once '../config/sslcommerz.php';

// Log IPN request
$log_data = "IPN Request: " . date('Y-m-d H:i:s') . "\n";
$log_data .= "POST Data: " . print_r($_POST, true) . "\n";
$log_data .= "GET Data: " . print_r($_GET, true) . "\n";
$log_data .= "---\n";
file_put_contents('../logs/ipn.log', $log_data, FILE_APPEND | LOCK_EX);

if ($_POST) {
    $tran_id = $_POST['tran_id'] ?? '';
    $val_id = $_POST['val_id'] ?? '';
    $amount = $_POST['amount'] ?? 0;
    $card_type = $_POST['card_type'] ?? '';
    $store_amount = $_POST['store_amount'] ?? 0;
    $bank_tran_id = $_POST['bank_tran_id'] ?? '';
    $status = $_POST['status'] ?? '';
    
    if ($status == 'VALID' && $tran_id && $val_id) {
        // Validate payment with SSL Commerz
        $validation = SSLCommerz::validate_payment($val_id, SSLC_STORE_ID, SSLC_STORE_PASSWORD);
        
        if ($validation && $validation['status'] == 'VALID') {
            try {
                // Find the order
                $stmt = $pdo->prepare("SELECT * FROM orders WHERE transaction_id = ?");
                $stmt->execute([$tran_id]);
                $order = $stmt->fetch();
                
                if ($order && $order['payment_status'] != 'paid') {
                    // Update order payment status
                    $stmt = $pdo->prepare("UPDATE orders SET 
                        payment_status = 'paid', 
                        payment_method = 'sslcommerz',
                        payment_details = ?,
                        updated_at = NOW() 
                        WHERE id = ?");
                    
                    $payment_details = json_encode([
                        'val_id' => $val_id,
                        'bank_tran_id' => $bank_tran_id,
                        'card_type' => $card_type,
                        'store_amount' => $store_amount,
                        'payment_time' => date('Y-m-d H:i:s'),
                        'ipn_processed' => true
                    ]);
                    
                    $stmt->execute([$payment_details, $order['id']]);
                    
                    // Log activity
                    log_activity('IPN Payment Success', "IPN processed for order #{$order['id']} - Amount: {$amount} BDT", $order['customer_id'], 'system');
                    
                    // Log success
                    $log_data = "IPN SUCCESS: Order #{$order['id']} payment confirmed\n";
                    file_put_contents('../logs/ipn.log', $log_data, FILE_APPEND | LOCK_EX);
                    
                    echo "SUCCESS";
                } else {
                    $log_data = "IPN ERROR: Order not found or already paid - Transaction: $tran_id\n";
                    file_put_contents('../logs/ipn.log', $log_data, FILE_APPEND | LOCK_EX);
                    echo "ORDER_NOT_FOUND";
                }
            } catch(PDOException $e) {
                $log_data = "IPN DATABASE ERROR: " . $e->getMessage() . "\n";
                file_put_contents('../logs/ipn.log', $log_data, FILE_APPEND | LOCK_EX);
                echo "DATABASE_ERROR";
            }
        } else {
            $log_data = "IPN VALIDATION FAILED: Transaction: $tran_id\n";
            file_put_contents('../logs/ipn.log', $log_data, FILE_APPEND | LOCK_EX);
            echo "VALIDATION_FAILED";
        }
    } else {
        $log_data = "IPN INVALID STATUS: Status: $status, Transaction: $tran_id\n";
        file_put_contents('../logs/ipn.log', $log_data, FILE_APPEND | LOCK_EX);
        echo "INVALID_STATUS";
    }
} else {
    echo "NO_POST_DATA";
}
?>
