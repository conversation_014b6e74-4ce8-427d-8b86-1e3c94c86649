<?php
require_once 'config/session.php';
require_once 'config/database.php';
require_once 'config/functions.php';

// Check if customer is logged in
if (!isset($_SESSION['customer_id'])) {
    redirect('login.php');
}

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $name = sanitize_input($_POST['name']);
        $email = sanitize_input($_POST['email']);
        $phone = sanitize_input($_POST['phone']);
        $address = sanitize_input($_POST['address']);
        $city = sanitize_input($_POST['city']);
        $postal_code = sanitize_input($_POST['postal_code']);
        
        // Handle profile image upload
        $profile_image = $_POST['existing_image'] ?? null;
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $upload_result = upload_image($_FILES['profile_image'], 'uploads/customers/');
            if ($upload_result['success']) {
                // Delete old image if exists
                if ($profile_image && file_exists('uploads/customers/' . $profile_image)) {
                    unlink('uploads/customers/' . $profile_image);
                }
                $profile_image = $upload_result['filename'];
            } else {
                $error = $upload_result['message'];
            }
        }
        
        if (!isset($error)) {
            // Validation
            if (empty($name) || empty($phone)) {
                $error = 'নাম এবং মোবাইল নম্বর আবশ্যক।';
            } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $error = 'সঠিক ইমেইল ঠিকানা দিন।';
            } elseif (strlen($phone) < 11) {
                $error = 'মোবাইল নম্বর কমপক্ষে ১১ সংখ্যার হতে হবে।';
            } else {
                try {
                    // Check if phone already exists for other customers
                    $stmt = $pdo->prepare("SELECT id FROM customers WHERE phone = ? AND id != ?");
                    $stmt->execute([$phone, $_SESSION['customer_id']]);
                    if ($stmt->fetch()) {
                        $error = 'এই মোবাইল নম্বর অন্য কাস্টমার ব্যবহার করছেন।';
                    } elseif (!empty($email)) {
                        // Check if email already exists for other customers
                        $stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ? AND id != ?");
                        $stmt->execute([$email, $_SESSION['customer_id']]);
                        if ($stmt->fetch()) {
                            $error = 'এই ইমেইল ঠিকানা অন্য কাস্টমার ব্যবহার করছেন।';
                        }
                    }
                    
                    if (!isset($error)) {
                        $email_value = !empty($email) ? $email : null;
                        $stmt = $pdo->prepare("UPDATE customers SET name = ?, email = ?, phone = ?, profile_image = ?, address = ?, city = ?, postal_code = ?, updated_at = NOW() WHERE id = ?");
                        $stmt->execute([$name, $email_value, $phone, $profile_image, $address, $city, $postal_code, $_SESSION['customer_id']]);
                        
                        // Update session
                        $_SESSION['customer_name'] = $name;
                        $_SESSION['customer_email'] = $email_value;
                        
                        $success = 'প্রোফাইল সফলভাবে আপডেট করা হয়েছে।';
                    }
                } catch(PDOException $e) {
                    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
                }
            }
        }
    }
    
    if ($action === 'change_password') {
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        if ($new_password !== $confirm_password) {
            $error = 'নতুন পাসওয়ার্ড মিলছে না।';
        } elseif (strlen($new_password) < 6) {
            $error = 'পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে।';
        } else {
            try {
                // Verify current password
                $stmt = $pdo->prepare("SELECT password FROM customers WHERE id = ?");
                $stmt->execute([$_SESSION['customer_id']]);
                $customer = $stmt->fetch();
                
                if (!password_verify($current_password, $customer['password'])) {
                    $error = 'বর্তমান পাসওয়ার্ড ভুল।';
                } else {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE customers SET password = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$hashed_password, $_SESSION['customer_id']]);
                    
                    $success = 'পাসওয়ার্ড সফলভাবে পরিবর্তন করা হয়েছে।';
                }
            } catch(PDOException $e) {
                $error = 'ডাটাবেস এরর: ' . $e->getMessage();
            }
        }
    }
}

// Get customer info
try {
    $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
    $stmt->execute([$_SESSION['customer_id']]);
    $customer = $stmt->fetch();
    
    if (!$customer) {
        session_destroy();
        redirect('login.php');
    }
    
    // Get customer statistics
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_orders, SUM(total_amount) as total_spent FROM orders WHERE customer_id = ?");
    $stmt->execute([$_SESSION['customer_id']]);
    $stats = $stmt->fetch();
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    $customer = [];
    $stats = ['total_orders' => 0, 'total_spent' => 0];
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>আমার প্রোফাইল - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .profile-container {
            max-width: 1000px;
            margin: 2rem auto;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 2rem;
        }
        .profile-sidebar {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            height: fit-content;
        }
        .profile-main {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .profile-avatar {
            text-align: center;
            margin-bottom: 2rem;
        }
        .avatar-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #667eea;
            margin-bottom: 1rem;
        }
        .default-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            margin: 0 auto 1rem;
        }
        .profile-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #667eea;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }
        .profile-tabs {
            display: flex;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 2rem;
        }
        .tab-button {
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .tab-button.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        @media (max-width: 768px) {
            .profile-container {
                grid-template-columns: 1fr;
            }
            .form-row {
                grid-template-columns: 1fr;
            }
            .profile-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-store"></i> <a href="index.php" style="color: white; text-decoration: none;">আমার দোকান</a></h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php">হোম</a></li>
                    <li><a href="products.php">পণ্যসমূহ</a></li>
                    <li><a href="cart.php">কার্ট <span id="cart-count">0</span></a></li>
                    <li><a href="profile.php">প্রোফাইল</a></li>
                    <li><a href="logout.php">লগআউট</a></li>
                    <li><a href="admin/login.php">অ্যাডমিন</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="profile-container">
                <!-- Profile Sidebar -->
                <aside class="profile-sidebar">
                    <div class="profile-avatar">
                        <?php if ($customer['profile_image']): ?>
                            <img src="uploads/customers/<?php echo $customer['profile_image']; ?>" 
                                 alt="<?php echo $customer['name']; ?>" class="avatar-image">
                        <?php else: ?>
                            <div class="default-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                        <?php endif; ?>
                        <h3><?php echo htmlspecialchars($customer['name']); ?></h3>
                        <p><?php echo htmlspecialchars($customer['email'] ?: $customer['phone']); ?></p>
                    </div>
                    
                    <div class="profile-stats">
                        <div class="stat-card">
                            <div class="stat-value"><?php echo $stats['total_orders'] ?: '0'; ?></div>
                            <div class="stat-label">মোট অর্ডার</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value"><?php echo $stats['total_spent'] ? format_price($stats['total_spent']) : '০ টাকা'; ?></div>
                            <div class="stat-label">মোট খরচ</div>
                        </div>
                    </div>
                    
                    <div style="text-align: center;">
                        <p><small>সদস্য হয়েছেন: <?php echo date('d/m/Y', strtotime($customer['created_at'])); ?></small></p>
                    </div>
                </aside>
                
                <!-- Profile Main -->
                <main class="profile-main">
                    <?php if (isset($success)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($error)): ?>
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Tabs -->
                    <div class="profile-tabs">
                        <button class="tab-button active" onclick="showTab('profile')">
                            <i class="fas fa-user"></i> প্রোফাইল তথ্য
                        </button>
                        <button class="tab-button" onclick="showTab('password')">
                            <i class="fas fa-lock"></i> পাসওয়ার্ড পরিবর্তন
                        </button>
                        <button class="tab-button" onclick="showTab('orders')">
                            <i class="fas fa-shopping-cart"></i> আমার অর্ডার
                        </button>
                    </div>
                    
                    <!-- Profile Tab -->
                    <div id="profile-tab" class="tab-content active">
                        <h3>প্রোফাইল তথ্য আপডেট করুন</h3>
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="update_profile">
                            <input type="hidden" name="existing_image" value="<?php echo $customer['profile_image']; ?>">
                            
                            <div class="form-group">
                                <label for="name">পূর্ণ নাম *</label>
                                <input type="text" name="name" id="name" class="form-control" 
                                       value="<?php echo htmlspecialchars($customer['name']); ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="profile_image">প্রোফাইল ছবি</label>
                                <input type="file" name="profile_image" id="profile_image" class="form-control" accept="image/*">
                                <small>নতুন ছবি আপলোড করলে পুরানো ছবি প্রতিস্থাপিত হবে।</small>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="phone">মোবাইল নম্বর *</label>
                                    <input type="tel" name="phone" id="phone" class="form-control" 
                                           value="<?php echo htmlspecialchars($customer['phone']); ?>" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="email">ইমেইল ঠিকানা (ঐচ্ছিক)</label>
                                    <input type="email" name="email" id="email" class="form-control" 
                                           value="<?php echo htmlspecialchars($customer['email'] ?: ''); ?>">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="address">ঠিকানা</label>
                                <textarea name="address" id="address" class="form-control" rows="3"><?php echo htmlspecialchars($customer['address'] ?: ''); ?></textarea>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="city">শহর</label>
                                    <input type="text" name="city" id="city" class="form-control" 
                                           value="<?php echo htmlspecialchars($customer['city'] ?: ''); ?>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="postal_code">পোস্টাল কোড</label>
                                    <input type="text" name="postal_code" id="postal_code" class="form-control" 
                                           value="<?php echo htmlspecialchars($customer['postal_code'] ?: ''); ?>">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> প্রোফাইল আপডেট করুন
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Password Tab -->
                    <div id="password-tab" class="tab-content">
                        <h3>পাসওয়ার্ড পরিবর্তন করুন</h3>
                        <form method="POST">
                            <input type="hidden" name="action" value="change_password">
                            
                            <div class="form-group">
                                <label for="current_password">বর্তমান পাসওয়ার্ড</label>
                                <input type="password" name="current_password" id="current_password" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="new_password">নতুন পাসওয়ার্ড</label>
                                <input type="password" name="new_password" id="new_password" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="confirm_password">নতুন পাসওয়ার্ড নিশ্চিত করুন</label>
                                <input type="password" name="confirm_password" id="confirm_password" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-key"></i> পাসওয়ার্ড পরিবর্তন করুন
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Orders Tab -->
                    <div id="orders-tab" class="tab-content">
                        <h3>আমার অর্ডার</h3>
                        <p>অর্ডার ইতিহাস শীঘ্রই আসছে!</p>
                    </div>
                </main>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2024 আমার দোকান। সকল অধিকার সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function showTab(tabName) {
            // Hide all tabs
            $('.tab-content').removeClass('active');
            $('.tab-button').removeClass('active');
            
            // Show selected tab
            $('#' + tabName + '-tab').addClass('active');
            $('[onclick="showTab(\'' + tabName + '\')"]').addClass('active');
        }
        
        $(document).ready(function() {
            updateCartCount();
            
            // Password confirmation validation
            $('#confirm_password').on('input', function() {
                const newPassword = $('#new_password').val();
                const confirmPassword = $(this).val();
                
                if (newPassword !== confirmPassword) {
                    this.setCustomValidity('পাসওয়ার্ড মিলছে না');
                } else {
                    this.setCustomValidity('');
                }
            });
        });
    </script>
</body>
</html>
