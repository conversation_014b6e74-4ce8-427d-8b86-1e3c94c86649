<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

$message = '';
$success = '';

// Handle form submission
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_ssl_settings') {
        $store_id = sanitize_input($_POST['store_id'] ?? '');
        $store_password = sanitize_input($_POST['store_password'] ?? '');
        $is_sandbox = isset($_POST['is_sandbox']) ? 1 : 0;
        $is_enabled = isset($_POST['is_enabled']) ? 1 : 0;
        
        if (empty($store_id) || empty($store_password)) {
            $message = 'Store ID এবং Store Password আবশ্যক।';
        } else {
            try {
                // Check if SSL settings exist
                $stmt = $pdo->query("SELECT COUNT(*) FROM settings WHERE setting_key LIKE 'ssl_%'");
                $exists = $stmt->fetchColumn() > 0;
                
                if ($exists) {
                    // Update existing settings
                    $settings = [
                        'ssl_store_id' => $store_id,
                        'ssl_store_password' => $store_password,
                        'ssl_is_sandbox' => $is_sandbox,
                        'ssl_is_enabled' => $is_enabled
                    ];
                    
                    foreach ($settings as $key => $value) {
                        $stmt = $pdo->prepare("UPDATE settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?");
                        $stmt->execute([$value, $key]);
                    }
                } else {
                    // Insert new settings
                    $settings = [
                        'ssl_store_id' => $store_id,
                        'ssl_store_password' => $store_password,
                        'ssl_is_sandbox' => $is_sandbox,
                        'ssl_is_enabled' => $is_enabled
                    ];
                    
                    foreach ($settings as $key => $value) {
                        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())");
                        $stmt->execute([$key, $value]);
                    }
                }
                
                log_activity('SSL Settings Updated', 'SSL Commerz settings updated', $_SESSION['admin_id'], 'admin');
                $success = 'SSL Commerz সেটিংস সফলভাবে আপডেট করা হয়েছে।';
                
            } catch(PDOException $e) {
                $message = 'ডাটাবেস এরর: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'test_connection') {
        // Test SSL Commerz connection
        $store_id = sanitize_input($_POST['test_store_id'] ?? '');
        $store_password = sanitize_input($_POST['test_store_password'] ?? '');
        $is_sandbox = isset($_POST['test_is_sandbox']) ? true : false;
        
        if (empty($store_id) || empty($store_password)) {
            $message = 'Test এর জন্য Store ID এবং Store Password আবশ্যক।';
        } else {
            // Test API endpoint
            $api_url = $is_sandbox ? 
                'https://sandbox.sslcommerz.com/gwprocess/v4/api.php' : 
                'https://securepay.sslcommerz.com/gwprocess/v4/api.php';
            
            $test_data = [
                'store_id' => $store_id,
                'store_passwd' => $store_password,
                'total_amount' => 10,
                'currency' => 'BDT',
                'tran_id' => 'TEST_' . time(),
                'success_url' => 'http://localhost/test',
                'fail_url' => 'http://localhost/test',
                'cancel_url' => 'http://localhost/test',
                'cus_name' => 'Test Customer',
                'cus_email' => '<EMAIL>',
                'cus_phone' => '01700000000',
                'cus_add1' => 'Test Address',
                'cus_city' => 'Dhaka',
                'cus_country' => 'Bangladesh',
                'product_name' => 'Test Product',
                'product_category' => 'Test'
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $test_data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code == 200 && $response) {
                $result = json_decode($response, true);
                if (isset($result['status']) && $result['status'] == 'SUCCESS') {
                    $success = 'SSL Commerz connection সফল! API কাজ করছে।';
                } else {
                    $message = 'SSL Commerz connection ব্যর্থ: ' . ($result['failedreason'] ?? 'Unknown error');
                }
            } else {
                $message = 'SSL Commerz API এ connection করতে পারেনি। HTTP Code: ' . $http_code;
            }
        }
    }
}

// Get current SSL settings
$ssl_settings = [
    'ssl_store_id' => '',
    'ssl_store_password' => '',
    'ssl_is_sandbox' => 1,
    'ssl_is_enabled' => 0
];

try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'ssl_%'");
    while ($row = $stmt->fetch()) {
        $ssl_settings[$row['setting_key']] = $row['setting_value'];
    }
} catch(PDOException $e) {
    // Settings table might not exist, ignore error
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL Commerz সেটিংস - <?php echo SITE_NAME; ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
        }
        .sidebar-header {
            text-align: center;
            padding: 0 1rem 2rem;
            border-bottom: 1px solid #34495e;
            margin-bottom: 2rem;
        }
        .sidebar-menu {
            list-style: none;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 1.5rem;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
            color: #ffd700;
        }
        .main-content {
            flex: 1;
            background: #f8f9fa;
        }
        .admin-header {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .admin-body {
            padding: 2rem;
        }
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            border: 1px solid transparent;
        }
        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .alert-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .ssl-config-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .config-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .config-section h3 {
            color: #2c5aa0;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e9ecef;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .status-indicator {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status-enabled {
            background: #d4edda;
            color: #155724;
        }
        .status-disabled {
            background: #f8d7da;
            color: #721c24;
        }
        .status-sandbox {
            background: #fff3cd;
            color: #856404;
        }
        .test-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
        }
        .credentials-info {
            background: #e7f3ff;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border-left: 4px solid #007bff;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-primary {
            background: #007bff;
        }
        .btn-info {
            background: #17a2b8;
        }
        .btn-secondary {
            background: #6c757d;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-store"></i> অ্যাডমিন প্যানেল</h2>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="products.php"><i class="fas fa-box"></i> পণ্য ব্যবস্থাপনা</a></li>
                <li><a href="categories.php"><i class="fas fa-tags"></i> ক্যাটেগরি</a></li>
                <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> অর্ডার</a></li>
                <li><a href="customers.php"><i class="fas fa-users"></i> কাস্টমার</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> সেটিংস</a></li>
                <li><a href="ssl_settings.php" class="active"><i class="fas fa-credit-card"></i> SSL Commerz</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> লগআউট</a></li>
            </ul>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="admin-header">
                <h1><i class="fas fa-credit-card"></i> SSL Commerz সেটিংস</h1>
                <div>
                    <span class="status-indicator <?php echo $ssl_settings['ssl_is_enabled'] ? 'status-enabled' : 'status-disabled'; ?>">
                        <?php echo $ssl_settings['ssl_is_enabled'] ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                    </span>
                    <?php if ($ssl_settings['ssl_is_sandbox']): ?>
                        <span class="status-indicator status-sandbox">Sandbox Mode</span>
                    <?php endif; ?>
                </div>
            </header>

            <div class="admin-body">
                <div class="ssl-config-container">
                    <?php if ($message): ?>
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                        </div>
                    <?php endif; ?>

                    <!-- SSL Commerz Configuration -->
                    <div class="config-section">
                        <h3><i class="fas fa-cog"></i> SSL Commerz কনফিগারেশন</h3>
                        
                        <div class="credentials-info">
                            <h4><i class="fas fa-info-circle"></i> SSL Commerz Account Setup</h4>
                            <p><strong>🔴 Live Account Required:</strong> Demo credentials শুধু testing এর জন্য</p>
                            <p><strong>📝 Account তৈরি করুন:</strong> <a href="https://sslcommerz.com/" target="_blank">SSL Commerz Website</a></p>
                            <p><strong>📋 Required Documents:</strong> Trade License, NID, Bank Statement</p>
                            <p><strong>⏱️ Verification Time:</strong> 1-3 business days</p>
                            <p><strong>💰 Charges:</strong> 2.9% + VAT per transaction</p>
                            <p><strong>📞 Support:</strong> +88-*************</p>
                            <hr>
                            <p><strong>Demo Credentials (Testing Only):</strong></p>
                            <p>Store ID: <code>testbox</code>, Password: <code>qwerty</code></p>
                            <p><a href="https://developer.sslcommerz.com/" target="_blank">Developer Documentation</a></p>
                        </div>

                        <form method="POST">
                            <input type="hidden" name="action" value="update_ssl_settings">
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="store_id">Store ID *</label>
                                    <input type="text" name="store_id" id="store_id" class="form-control" 
                                           value="<?php echo htmlspecialchars($ssl_settings['ssl_store_id']); ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="store_password">Store Password *</label>
                                    <input type="password" name="store_password" id="store_password" class="form-control" 
                                           value="<?php echo htmlspecialchars($ssl_settings['ssl_store_password']); ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" name="is_sandbox" <?php echo $ssl_settings['ssl_is_sandbox'] ? 'checked' : ''; ?>>
                                        <span style="color: #ff6b35;">⚠️ Sandbox Mode (Test Environment)</span>
                                    </label>
                                    <small style="color: #dc3545;">
                                        <strong>Warning:</strong> Sandbox mode এ real payment হয় না।
                                        Live payments এর জন্য uncheck করুন এবং real credentials ব্যবহার করুন।
                                    </small>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" name="is_enabled" <?php echo $ssl_settings['ssl_is_enabled'] ? 'checked' : ''; ?>>
                                        SSL Commerz সক্রিয় করুন
                                    </label>
                                    <small>Payment gateway enable/disable করুন</small>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> সেটিংস সংরক্ষণ করুন
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Test Connection -->
                    <div class="config-section">
                        <h3><i class="fas fa-plug"></i> Connection Test</h3>
                        
                        <div class="test-section">
                            <h4>SSL Commerz API Test</h4>
                            <p>আপনার credentials test করুন SSL Commerz API এর সাথে connection check করতে।</p>
                            
                            <form method="POST" style="display: inline-block;">
                                <input type="hidden" name="action" value="test_connection">
                                <input type="hidden" name="test_store_id" value="<?php echo htmlspecialchars($ssl_settings['ssl_store_id']); ?>">
                                <input type="hidden" name="test_store_password" value="<?php echo htmlspecialchars($ssl_settings['ssl_store_password']); ?>">
                                <input type="hidden" name="test_is_sandbox" value="<?php echo $ssl_settings['ssl_is_sandbox'] ? '1' : ''; ?>">
                                
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-plug"></i> Connection Test করুন
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Current Configuration Display -->
                    <div class="config-section">
                        <h3><i class="fas fa-eye"></i> বর্তমান কনফিগারেশন</h3>
                        
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px;">
                            <div style="margin-bottom: 0.5rem;">
                                <strong>Store ID:</strong> <?php echo $ssl_settings['ssl_store_id'] ? htmlspecialchars($ssl_settings['ssl_store_id']) : 'Not Set'; ?>
                            </div>
                            <div style="margin-bottom: 0.5rem;">
                                <strong>Environment:</strong> <?php echo $ssl_settings['ssl_is_sandbox'] ? 'Sandbox (Test)' : 'Live (Production)'; ?>
                            </div>
                            <div style="margin-bottom: 0.5rem;">
                                <strong>Status:</strong> <?php echo $ssl_settings['ssl_is_enabled'] ? 'Enabled' : 'Disabled'; ?>
                            </div>
                            <div>
                                <strong>API URL:</strong> 
                                <?php echo $ssl_settings['ssl_is_sandbox'] ? 
                                    'https://sandbox.sslcommerz.com/gwprocess/v4/api.php' : 
                                    'https://securepay.sslcommerz.com/gwprocess/v4/api.php'; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
