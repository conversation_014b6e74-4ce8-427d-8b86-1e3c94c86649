<?php
require_once '../config/session.php';
require_once '../config/database.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

try {
    $customer_id = $_SESSION['customer_id'];
    
    // Load cart from database
    $stmt = $pdo->prepare("SELECT product_id, quantity FROM cart WHERE customer_id = ?");
    $stmt->execute([$customer_id]);
    $db_cart = $stmt->fetchAll();
    
    if (!empty($db_cart)) {
        // Convert to session format
        $_SESSION['cart'] = [];
        foreach ($db_cart as $item) {
            $_SESSION['cart'][$item['product_id']] = $item['quantity'];
        }
        
        echo json_encode(['success' => true, 'message' => 'Cart loaded from database', 'cart' => $_SESSION['cart']]);
    } else {
        echo json_encode(['success' => false, 'message' => 'No cart found in database']);
    }
    
} catch(PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
