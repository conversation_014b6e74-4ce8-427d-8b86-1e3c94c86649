<?php
require_once 'config/session.php';
require_once 'config/database.php';

// Check if product ID and quantity are provided
if (!isset($_POST['product_id']) || empty($_POST['product_id']) || !isset($_POST['quantity']) || empty($_POST['quantity'])) {
    redirect('products.php');
}

$product_id = (int)$_POST['product_id'];
$quantity = (int)$_POST['quantity'];

// Fetch product details
try {
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();

    if ($product && $product['stock_quantity'] >= $quantity) {
        // Add product to cart
        $cart = isset($_SESSION['cart']) ? $_SESSION['cart'] : [];

        if (isset($cart[$product_id])) {
            $cart[$product_id]['quantity'] += $quantity;
        } else {
            $cart[$product_id] = [
                'name' => $product['name'],
                'price' => $product['discount_price'] ?: $product['price'],
                'quantity' => $quantity,
                'image' => $product['image']
            ];
        }

        $_SESSION['cart'] = $cart;

        // Redirect to checkout page
        redirect('checkout.php');
    } else {
        // Redirect back to product page with an error message
        redirect('product_details.php?id=' . $product_id . '&error=stock');
    }
} catch (PDOException $e) {
    // Handle database error
    redirect('product_details.php?id=' . $product_id . '&error=db');
}
?>