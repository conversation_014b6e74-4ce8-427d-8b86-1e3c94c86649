-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 17, 2025 at 10:54 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `dokan_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `activity_logs`
--

CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user_type` enum('admin','customer') DEFAULT 'admin',
  `action` varchar(100) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `activity_logs`
--

INSERT INTO `activity_logs` (`id`, `user_id`, `user_type`, `action`, `details`, `ip_address`, `created_at`) VALUES
(1, 1, 'admin', 'admin_login', 'Admin logged in', '::1', '2025-07-16 05:33:41'),
(2, 1, 'admin', 'admin_logout', 'Admin logged out', '::1', '2025-07-16 06:27:24'),
(3, 2, 'customer', 'Customer Registration', 'Test customer registered: Test User (01712345678)', '::1', '2025-07-16 07:18:07'),
(4, 3, 'customer', 'Customer Registration', 'New customer registered: আসফ (01977861762)', '::1', '2025-07-16 15:20:42'),
(5, 4, 'customer', 'Customer Registration', 'New customer registered: Ashof (01717861762)', '::1', '2025-07-16 15:38:55'),
(6, 1, 'admin', 'admin_login', 'Admin logged in', '::1', '2025-07-16 15:57:27'),
(7, 1, 'admin', 'customer_deleted', 'Deleted customer ID: 2', '::1', '2025-07-16 15:58:24'),
(8, 1, 'admin', 'customer_deleted', 'Deleted customer ID: 2', '::1', '2025-07-16 15:58:27'),
(9, 1, 'admin', 'customer_deleted', 'Deleted customer ID: 2', '::1', '2025-07-16 15:58:30'),
(10, 1, 'admin', 'customer_deleted', 'Deleted customer ID: 2', '::1', '2025-07-16 15:58:38'),
(11, 1, 'admin', 'customer_deleted', 'Deleted customer ID: 2', '::1', '2025-07-16 15:58:42'),
(12, 1, 'admin', 'customer_deleted', 'Deleted customer ID: 3', '::1', '2025-07-16 15:58:48'),
(13, 1, 'admin', 'customer_deleted', 'Deleted customer ID: 4', '::1', '2025-07-16 15:58:55'),
(14, 1, 'admin', 'customer_deleted', 'Deleted customer ID: 4', '::1', '2025-07-16 15:58:58'),
(15, 1, 'admin', 'admin_logout', 'Admin logged out', '::1', '2025-07-16 16:00:14'),
(16, 1, 'admin', 'admin_login', 'Admin logged in', '::1', '2025-07-16 16:02:07'),
(17, 1, 'admin', 'admin_logout', 'Admin logged out', '::1', '2025-07-16 16:02:22'),
(18, 1, 'admin', 'admin_login', 'Admin logged in', '::1', '2025-07-16 16:09:52'),
(19, 1, 'admin', 'customer_deleted', 'Deleted customer ID: 2', '::1', '2025-07-16 16:10:49'),
(20, 1, 'admin', 'customer_deleted', 'Deleted customer ID: 3', '::1', '2025-07-16 16:10:55'),
(21, 1, 'admin', 'customer_deleted', 'Deleted customer ID: 3', '::1', '2025-07-16 16:13:03'),
(22, 1, 'admin', 'admin_logout', 'Admin logged out', '::1', '2025-07-16 16:17:42'),
(23, 4, 'customer', 'Order Placed', 'Order #1 placed for 110,000.00 টাকা', '::1', '2025-07-16 16:25:03'),
(24, 4, 'customer', 'Order Placed', 'Order #2 placed for 110,000.00 টাকা', '::1', '2025-07-16 16:33:53'),
(25, 1, 'admin', 'admin_login', 'Admin logged in', '::1', '2025-07-16 16:36:23'),
(26, 1, 'admin', 'admin_login', 'Admin logged in', '::1', '2025-07-16 16:39:54'),
(27, 1, 'admin', 'order_status_updated', 'Updated order #1 status to processing', '::1', '2025-07-16 17:30:16'),
(28, 1, 'admin', 'order_status_updated', 'Updated order #2 status to processing', '::1', '2025-07-16 17:30:27'),
(29, 1, 'admin', 'order_status_updated', 'Updated order #2 status to confirmed', '::1', '2025-07-16 17:32:04'),
(30, 1, 'admin', 'order_status_updated', 'Updated order #1 status to confirmed', '::1', '2025-07-16 17:32:14'),
(31, 4, 'customer', 'Order Placed', 'Order #3 placed for 35,000.00 টাকা', '::1', '2025-07-17 03:28:40'),
(32, 1, 'admin', 'admin_login', 'Admin logged in', '::1', '2025-07-17 03:29:54'),
(33, 4, 'customer', 'Order Placed', 'Order #4 placed for 35,900.00 টাকা', '::1', '2025-07-17 03:30:58'),
(34, 1, 'admin', 'admin_login', 'Admin logged in', '::1', '2025-07-17 03:39:55'),
(35, 1, '', 'salesman_login', 'Salesman logged in: রহিম উদ্দিন', '::1', '2025-07-17 04:02:33'),
(36, 1, '', 'pos_sale', 'POS Sale: POS202507175024', '::1', '2025-07-17 04:03:41'),
(37, 1, 'admin', 'admin_login', 'Admin logged in', '::1', '2025-07-17 04:27:33'),
(38, 1, '', 'salesman_login', 'Salesman logged in: রহিম উদ্দিন', '::1', '2025-07-17 04:40:24'),
(39, 1, '', 'salesman_logout', 'Salesman logged out: রহিম উদ্দিন', '::1', '2025-07-17 04:45:52'),
(40, 1, '', 'salesman_login', 'Salesman logged in: রহিম উদ্দিন', '::1', '2025-07-17 04:46:15'),
(41, 1, '', 'salesman_logout', 'Salesman logged out: রহিম উদ্দিন', '::1', '2025-07-17 04:46:17'),
(42, 1, '', 'salesman_login', 'Salesman logged in: রহিম উদ্দিন', '::1', '2025-07-17 04:46:19'),
(43, 1, '', 'pos_sale', 'POS Sale: POS202507172409', '::1', '2025-07-17 04:47:42'),
(44, 1, '', 'salesman_login', 'Salesman logged in: রহিম উদ্দিন', '::1', '2025-07-17 05:37:31'),
(45, 1, '', 'salesman_logout', 'Salesman logged out: রহিম উদ্দিন', '::1', '2025-07-17 05:50:27'),
(46, 1, 'admin', 'admin_login', 'Admin logged in', '::1', '2025-07-17 05:50:47'),
(47, 1, '', 'salesman_login', 'Salesman logged in: রহিম উদ্দিন', '::1', '2025-07-17 05:53:14'),
(48, 1, '', 'salesman_logout', 'Salesman logged out: রহিম উদ্দিন', '::1', '2025-07-17 05:53:26'),
(49, 1, 'admin', 'admin_logout', 'Admin logged out', '::1', '2025-07-17 05:53:58'),
(50, 1, '', 'salesman_login', 'Salesman logged in: রহিম উদ্দিন', '::1', '2025-07-17 05:57:26'),
(51, 1, 'admin', 'admin_login', 'Admin logged in', '::1', '2025-07-17 06:04:05'),
(52, 1, 'admin', 'admin_logout', 'Admin logged out', '::1', '2025-07-17 06:05:38'),
(53, 1, '', 'salesman_login', 'Salesman logged in: রহিম উদ্দিন', '::1', '2025-07-17 06:06:01');

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('admin','manager') DEFAULT 'admin',
  `status` enum('active','inactive') DEFAULT 'active',
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `email`, `password`, `full_name`, `role`, `status`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$RaDyint2QQ3kIkcwvufPdu9M1Jb3hW2BdLPch6xhOqtlf3P4Kb1ZG', 'System Administrator', 'admin', 'active', '2025-07-17 06:04:05', '2025-07-16 05:23:49', '2025-07-17 06:04:05');

-- --------------------------------------------------------

--
-- Table structure for table `cart`
--

CREATE TABLE `cart` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cart`
--

INSERT INTO `cart` (`id`, `customer_id`, `product_id`, `quantity`, `created_at`, `updated_at`) VALUES
(4, 4, 1, 1, '2025-07-17 03:30:33', '2025-07-17 03:30:33'),
(5, 4, 5, 2, '2025-07-17 03:30:33', '2025-07-17 03:30:33');

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `description`, `image`, `status`, `created_at`, `updated_at`) VALUES
(1, 'ইলেকট্রনিক্স', 'মোবাইল, ল্যাপটপ, টিভি এবং অন্যান্য ইলেকট্রনিক পণ্য', NULL, 'active', '2025-07-16 05:23:49', '2025-07-16 05:23:49'),
(2, 'ফ্যাশন', 'পুরুষ ও মহিলাদের পোশাক, জুতা এবং এক্সেসরিজ', NULL, 'active', '2025-07-16 05:23:49', '2025-07-16 05:23:49'),
(3, 'বই', 'শিক্ষামূলক বই, উপন্যাস, গল্পের বই', NULL, 'active', '2025-07-16 05:23:49', '2025-07-16 05:23:49'),
(4, 'খাবার', 'স্থানীয় এবং আমদানিকৃত খাবার', NULL, 'active', '2025-07-16 05:23:49', '2025-07-16 05:23:49'),
(5, 'স্বাস্থ্য ও সৌন্দর্য', 'স্বাস্থ্য পণ্য, প্রসাধনী এবং ব্যক্তিগত যত্ন', NULL, 'active', '2025-07-16 05:23:49', '2025-07-16 05:23:49');

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `postal_code` varchar(10) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`id`, `name`, `email`, `password`, `phone`, `profile_image`, `address`, `city`, `postal_code`, `status`, `created_at`, `updated_at`) VALUES
(4, 'Ashof', '<EMAIL>', '$2y$10$KGCsl9Kh5cWm4lhxSVsx4eoQEHBxRn9lwFMXKPaf74.41ht8LAn6W', '01717861762', NULL, NULL, NULL, NULL, 'active', '2025-07-16 15:38:55', '2025-07-16 16:09:15');

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `order_number` varchar(20) NOT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `status` enum('pending','confirmed','processing','shipped','delivered','cancelled') DEFAULT 'pending',
  `payment_status` enum('pending','paid','failed','refunded') DEFAULT 'pending',
  `payment_method` enum('cash_on_delivery','bkash','nagad','bank_transfer') DEFAULT 'cash_on_delivery',
  `payment_details` text DEFAULT NULL,
  `shipping_address` text NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `customer_id`, `order_number`, `transaction_id`, `total_amount`, `status`, `payment_status`, `payment_method`, `payment_details`, `shipping_address`, `notes`, `created_at`, `updated_at`) VALUES
(1, 4, 'ORD202507168726', NULL, 110000.00, 'confirmed', 'pending', 'cash_on_delivery', NULL, 'দামুড়হুদা', '', '2025-07-16 16:25:03', '2025-07-16 17:32:13'),
(2, 4, 'ORD202507162890', 'TXN202507161833533108', 110000.00, 'confirmed', 'pending', '', NULL, 'দামুড়হুদা', '', '2025-07-16 16:33:53', '2025-07-16 17:32:04'),
(3, 4, 'ORD202507170097', 'TXN202507170528399130', 35000.00, 'pending', 'pending', '', NULL, 'ddd', '', '2025-07-17 03:28:39', '2025-07-17 03:28:39'),
(4, 4, 'ORD202507170470', 'TXN202507170530583704', 35900.00, 'pending', 'pending', '', NULL, 'fdfsdfsd', '', '2025-07-17 03:30:58', '2025-07-17 03:30:58');

-- --------------------------------------------------------

--
-- Table structure for table `order_items`
--

CREATE TABLE `order_items` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `total` decimal(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `order_items`
--

INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `price`, `total`) VALUES
(1, 1, 2, 2, 55000.00, 110000.00),
(2, 2, 2, 2, 55000.00, 110000.00),
(3, 3, 1, 1, 35000.00, 35000.00),
(4, 4, 1, 1, 35000.00, 35000.00),
(5, 4, 5, 2, 450.00, 900.00);

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `used` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `pos_sales`
--

CREATE TABLE `pos_sales` (
  `id` int(11) NOT NULL,
  `sale_number` varchar(50) NOT NULL,
  `salesman_id` int(11) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `customer_name` varchar(100) DEFAULT NULL,
  `customer_phone` varchar(20) DEFAULT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `final_amount` decimal(10,2) NOT NULL,
  `payment_method` enum('cash','card','mobile_banking') DEFAULT 'cash',
  `payment_status` enum('paid','partial','pending') DEFAULT 'paid',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `pos_sales`
--

INSERT INTO `pos_sales` (`id`, `sale_number`, `salesman_id`, `customer_id`, `customer_name`, `customer_phone`, `total_amount`, `discount_amount`, `final_amount`, `payment_method`, `payment_status`, `notes`, `created_at`) VALUES
(1, 'POS202507175024', 1, NULL, 'আলিফ', '০১৪২০১২১৪১৬', 35300.00, 0.00, 35300.00, 'cash', 'paid', '', '2025-07-17 04:03:41'),
(2, 'POS202507172409', 1, NULL, 'আলিফা', '০১৪২০১২১৪১৬', 220000.00, 0.00, 220000.00, 'cash', 'paid', '', '2025-07-17 04:47:42');

-- --------------------------------------------------------

--
-- Table structure for table `pos_sale_items`
--

CREATE TABLE `pos_sale_items` (
  `id` int(11) NOT NULL,
  `sale_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `pos_sale_items`
--

INSERT INTO `pos_sale_items` (`id`, `sale_id`, `product_id`, `quantity`, `unit_price`, `total_price`) VALUES
(1, 1, 4, 1, 300.00, 300.00),
(2, 1, 1, 1, 35000.00, 35000.00),
(3, 2, 2, 4, 55000.00, 220000.00);

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `name` varchar(200) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `discount_price` decimal(10,2) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `stock_quantity` int(11) DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `featured` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `name`, `description`, `price`, `discount_price`, `category_id`, `image`, `stock_quantity`, `status`, `featured`, `created_at`, `updated_at`) VALUES
(1, 'Samsung Galaxy A54', 'সর্বশেষ Samsung Galaxy A54 স্মার্টফোন', 35000.00, NULL, 1, NULL, 49, 'active', 1, '2025-07-16 05:23:49', '2025-07-17 04:03:41'),
(2, 'Dell Inspiron Laptop', 'Dell Inspiron 15 3000 সিরিজ ল্যাপটপ', 55000.00, NULL, 1, NULL, 21, 'active', 1, '2025-07-16 05:23:49', '2025-07-17 04:47:42'),
(3, 'পুরুষদের শার্ট', 'উচ্চ মানের কটন শার্ট', 1200.00, NULL, 2, NULL, 100, 'active', 0, '2025-07-16 05:23:49', '2025-07-16 05:23:49'),
(4, 'বাংলা উপন্যাস', 'জনপ্রিয় বাংলা উপন্যাস সংগ্রহ', 300.00, NULL, 3, NULL, 199, 'active', 0, '2025-07-16 05:23:49', '2025-07-17 04:03:41'),
(5, 'বাদাম মিশ্রণ', 'স্বাস্থ্যকর বাদাম মিশ্রণ', 450.00, NULL, 4, NULL, 80, 'active', 1, '2025-07-16 05:23:49', '2025-07-16 05:23:49');

-- --------------------------------------------------------

--
-- Table structure for table `salesman`
--

CREATE TABLE `salesman` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `employee_id` varchar(50) DEFAULT NULL,
  `commission_rate` decimal(5,2) DEFAULT 0.00,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `salesman`
--

INSERT INTO `salesman` (`id`, `name`, `email`, `phone`, `password`, `employee_id`, `commission_rate`, `status`, `created_at`, `updated_at`) VALUES
(1, 'রহিম উদ্দিন', '<EMAIL>', '01700000000', '$2y$10$7bhNfAsozJ2JVTXwnpoBgusM0sDQ6UvHz7TC3GNIqaMCDRU5Bq/Ue', 'EMP001', 5.00, 'active', '2025-07-17 03:58:07', '2025-07-17 06:05:35');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
(1, 'ssl_store_id', 'testbox', '2025-07-16 16:38:46', '2025-07-16 16:38:46'),
(2, 'ssl_store_password', 'qwerty', '2025-07-16 16:38:47', '2025-07-16 16:38:47'),
(3, 'ssl_is_sandbox', '1', '2025-07-16 16:38:47', '2025-07-16 16:38:47'),
(4, 'ssl_is_enabled', '1', '2025-07-16 16:38:47', '2025-07-17 03:30:09');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `cart`
--
ALTER TABLE `cart`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_cart_item` (`customer_id`,`product_id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_phone` (`phone`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `email_2` (`email`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `order_number` (`order_number`),
  ADD KEY `customer_id` (`customer_id`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_expires` (`expires_at`);

--
-- Indexes for table `pos_sales`
--
ALTER TABLE `pos_sales`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `sale_number` (`sale_number`),
  ADD KEY `salesman_id` (`salesman_id`),
  ADD KEY `fk_pos_sales_customer` (`customer_id`);

--
-- Indexes for table `pos_sale_items`
--
ALTER TABLE `pos_sale_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sale_id` (`sale_id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `salesman`
--
ALTER TABLE `salesman`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `employee_id` (`employee_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activity_logs`
--
ALTER TABLE `activity_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=54;

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `cart`
--
ALTER TABLE `cart`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pos_sales`
--
ALTER TABLE `pos_sales`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `pos_sale_items`
--
ALTER TABLE `pos_sale_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `salesman`
--
ALTER TABLE `salesman`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `cart`
--
ALTER TABLE `cart`
  ADD CONSTRAINT `cart_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cart_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `order_items`
--
ALTER TABLE `order_items`
  ADD CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD CONSTRAINT `password_reset_tokens_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pos_sales`
--
ALTER TABLE `pos_sales`
  ADD CONSTRAINT `fk_pos_sales_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `pos_sales_ibfk_1` FOREIGN KEY (`salesman_id`) REFERENCES `salesman` (`id`);

--
-- Constraints for table `pos_sale_items`
--
ALTER TABLE `pos_sale_items`
  ADD CONSTRAINT `pos_sale_items_ibfk_1` FOREIGN KEY (`sale_id`) REFERENCES `pos_sales` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pos_sale_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`);

--
-- Constraints for table `products`
--
ALTER TABLE `products`
  ADD CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
