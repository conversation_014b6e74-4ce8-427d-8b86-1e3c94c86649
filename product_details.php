<?php
require_once 'config/session.php';
require_once 'config/database.php';
require_once 'includes/image_handler.php';

// Check if product ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('products.php');
}

$product_id = (int)$_GET['id'];

// Fetch product details
try {
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.id = ? AND p.status = 'active'");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();

    if (!$product) {
        $error = "পণ্যটি খুঁজে পাওয়া যায়নি।";
    }
} catch (PDOException $e) {
    $error = "ডাটাবেস এরর: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($product['name']) ? htmlspecialchars($product['name']) : 'পণ্যের বিবরণ'; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .product-details-layout {
            display: grid;
            grid-template-columns: 1fr 1.5fr;
            gap: 3rem;
            margin-top: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .product-image-gallery {
            text-align: center;
        }
        .main-image {
            width: 100%;
            max-width: 400px;
            height: auto;
            border-radius: 10px;
            margin-bottom: 1rem;
            border: 1px solid #eee;
        }
        .product-info-details h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #333;
        }
        .product-category {
            font-size: 1rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .price-section {
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }
        .current-price {
            color: #2ed573;
            font-weight: 600;
        }
        .original-price-details {
            text-decoration: line-through;
            color: #ff4757;
            font-size: 1.5rem;
            margin-left: 1rem;
        }
        .stock-status {
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            display: inline-block;
        }
        .stock-available { background-color: #d4edda; color: #155724; }
        .stock-low { background-color: #fff3cd; color: #856404; }
        .stock-out { background-color: #f8d7da; color: #721c24; }

        .quantity-selector {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 2rem;
        }
        .quantity-selector label {
            font-size: 1.1rem;
            font-weight: 500;
        }
        .quantity-selector input {
            width: 60px;
            text-align: center;
        }
        .action-buttons .btn {
            padding: 1rem 2rem;
            font-size: 1.2rem;
        }
        .product-description-full {
            margin-top: 2rem;
            line-height: 1.8;
            color: #555;
        }

        @media (max-width: 768px) {
            .product-details-layout {
                grid-template-columns: 1fr;
            }
            .product-info-details h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-store"></i> <a href="index.php" style="color: white; text-decoration: none;">আমার দোকান</a></h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php">হোম</a></li>
                    <li><a href="products.php">পণ্যসমূহ</a></li>
                    <li><a href="cart.php"><i class="fas fa-shopping-cart"></i> কার্ট <span id="cart-count" style="background: #ff4757; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.8rem; margin-left: 0.3rem;">0</span></a></li>
                    <?php if(isset($_SESSION['customer_id'])): ?>
                        <li><a href="profile.php"><i class="fas fa-user"></i> প্রোফাইল</a></li>
                        <li><a href="logout.php">লগআউট</a></li>
                    <?php else: ?>
                        <li><a href="login.php">লগইন</a></li>
                        <li><a href="register.php">রেজিস্টার</a></li>
                    <?php endif; ?>
                    <li><a href="admin/login.php">অ্যাডমিন</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <?php if (isset($error)): ?>
                <div class="alert alert-error" style="text-align: center; padding: 2rem;">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                    <p><?php echo $error; ?></p>
                    <a href="products.php" class="btn btn-primary" style="margin-top: 1rem;">সকল পণ্য দেখুন</a>
                </div>
            <?php elseif (isset($product)): ?>
                <div class="product-details-layout">
                    <div class="product-image-gallery">
                        <?php echo ImageHandler::getImageHTML($product['image'], $product['name'], 'main-image', '', $product['id']); ?>
                    </div>
                    <div class="product-info-details">
                        <h1><?php echo htmlspecialchars($product['name']); ?></h1>
                        <div class="product-category">
                            ক্যাটেগরি: <a href="products.php?category=<?php echo $product['category_id']; ?>"><?php echo htmlspecialchars($product['category_name']); ?></a>
                        </div>
                        
                        <div class="price-section">
                            <?php if ($product['discount_price']): ?>
                                <span class="current-price"><?php echo format_price($product['discount_price']); ?></span>
                                <span class="original-price-details"><?php echo format_price($product['price']); ?></span>
                            <?php else: ?>
                                <span class="current-price"><?php echo format_price($product['price']); ?></span>
                            <?php endif; ?>
                        </div>

                        <div class="stock-status <?php
                            if ($product['stock_quantity'] == 0) echo 'stock-out';
                            elseif ($product['stock_quantity'] <= 5) echo 'stock-low';
                            else echo 'stock-available';
                        ?>">
                            <?php
                                if ($product['stock_quantity'] == 0) echo 'স্টক আউট';
                                elseif ($product['stock_quantity'] <= 5) echo 'সীমিত স্টক (' . $product['stock_quantity'] . ' টি বাকি)';
                                else echo 'স্টকে আছে';
                            ?>
                        </div>

                        <div class="product-description-full">
                            <p><?php echo nl2br(htmlspecialchars($product['description'])); ?></p>
                        </div>

                        <?php if ($product['stock_quantity'] > 0): ?>
                            <div class="quantity-selector">
                                <label for="quantity">পরিমাণ:</label>
                                <input type="number" id="quantity" name="quantity" value="1" min="1" max="<?php echo $product['stock_quantity']; ?>" class="form-control">
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-primary" onclick="addToCartWithQuantity(<?php echo $product['id']; ?>, '<?php echo addslashes(htmlspecialchars($product['name'])); ?>', <?php echo $product['discount_price'] ?: $product['price']; ?>)">
                                    <i class="fas fa-cart-plus"></i> কার্টে যোগ করুন
                                </button>
                                <form action="buy_now.php" method="POST" style="display: inline-block;">
                                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                    <input type="hidden" name="quantity" id="buy_now_quantity" value="1">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-bolt"></i> এখনই কিনুন
                                    </button>
                                </form>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2024 আমার দোকান। সকল অধিকার সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/jquery.min.js"></script>
    <script>
        const isLoggedIn = <?php echo isset($_SESSION['customer_id']) ? 'true' : 'false'; ?>;
    </script>
    <script src="assets/js/main.js"></script>
    <script>
        function addToCartWithQuantity(productId, productName, price) {
            const quantity = parseInt($('#quantity').val());
            if (quantity > 0) {
                addToCart(productId, productName, price, quantity);
            } else {
                alert('দয়া করে সঠিক পরিমাণ দিন।');
            }
        }

        $(document).ready(function() {
            updateCartCount();

            // Update buy now quantity
            $('#quantity').on('change', function() {
                $('#buy_now_quantity').val($(this).val());
            });
        });
    </script>
</body>
</html>
