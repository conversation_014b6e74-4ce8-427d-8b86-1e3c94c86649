<?php
// Cleanup Script for Password Reset Tokens
// Run this script periodically (e.g., via cron job) to clean up expired tokens

require_once 'config/database.php';
require_once 'config/email.php';

echo "<h2>পাসওয়ার্ড রিসেট টোকেন পরিষ্কার</h2>";

try {
    // Get statistics before cleanup
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM password_reset_tokens");
    $totalBefore = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as expired FROM password_reset_tokens WHERE expires_at < NOW()");
    $expiredCount = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as used FROM password_reset_tokens WHERE used = TRUE");
    $usedCount = $stmt->fetchColumn();
    
    echo "<h3>পরিষ্কার করার আগে:</h3>";
    echo "<ul>";
    echo "<li>মোট টোকেন: $totalBefore</li>";
    echo "<li>মেয়াদোত্তীর্ণ টোকেন: $expiredCount</li>";
    echo "<li>ব্যবহৃত টোকেন: $usedCount</li>";
    echo "</ul>";
    
    // Clean up expired and used tokens
    $result = cleanup_expired_tokens();
    
    if ($result['success']) {
        // Get statistics after cleanup
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM password_reset_tokens");
        $totalAfter = $stmt->fetchColumn();
        
        $cleaned = $totalBefore - $totalAfter;
        
        echo "<h3 style='color: green;'>✅ পরিষ্কার সম্পন্ন!</h3>";
        echo "<ul>";
        echo "<li>পরিষ্কার করা টোকেন: $cleaned</li>";
        echo "<li>অবশিষ্ট টোকেন: $totalAfter</li>";
        echo "</ul>";
        
        if ($cleaned > 0) {
            echo "<p style='color: green;'>$cleaned টি মেয়াদোত্তীর্ণ/ব্যবহৃত টোকেন সফলভাবে মুছে ফেলা হয়েছে।</p>";
        } else {
            echo "<p style='color: blue;'>কোন মেয়াদোত্তীর্ণ টোকেন পাওয়া যায়নি।</p>";
        }
        
        // Show recent password reset activity
        echo "<h3>সাম্প্রতিক পাসওয়ার্ড রিসেট কার্যকলাপ:</h3>";
        
        $stmt = $pdo->query("
            SELECT 
                prt.created_at,
                prt.used,
                prt.expires_at,
                c.name,
                c.email,
                c.phone
            FROM password_reset_tokens prt
            JOIN customers c ON prt.customer_id = c.id
            ORDER BY prt.created_at DESC
            LIMIT 10
        ");
        $recentTokens = $stmt->fetchAll();
        
        if ($recentTokens) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 1rem 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>তারিখ</th>";
            echo "<th style='padding: 8px;'>কাস্টমার</th>";
            echo "<th style='padding: 8px;'>যোগাযোগ</th>";
            echo "<th style='padding: 8px;'>স্ট্যাটাস</th>";
            echo "<th style='padding: 8px;'>মেয়াদ</th>";
            echo "</tr>";
            
            foreach ($recentTokens as $token) {
                $status = $token['used'] ? 'ব্যবহৃত' : (strtotime($token['expires_at']) < time() ? 'মেয়াদোত্তীর্ণ' : 'সক্রিয়');
                $statusColor = $token['used'] ? 'green' : (strtotime($token['expires_at']) < time() ? 'red' : 'blue');
                
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . date('d/m/Y H:i', strtotime($token['created_at'])) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($token['name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($token['email'] ?: $token['phone']) . "</td>";
                echo "<td style='padding: 8px; color: $statusColor;'><strong>$status</strong></td>";
                echo "<td style='padding: 8px;'>" . date('d/m/Y H:i', strtotime($token['expires_at'])) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>কোন সাম্প্রতিক পাসওয়ার্ড রিসেট কার্যকলাপ নেই।</p>";
        }
        
        // Show password reset statistics
        $stats_result = get_password_reset_stats();
        if ($stats_result['success']) {
            $stats = $stats_result['stats'];
            echo "<h3>পাসওয়ার্ড রিসেট পরিসংখ্যান:</h3>";
            echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 1rem 0;'>";
            
            echo "<div style='background: #e3f2fd; padding: 1rem; border-radius: 8px; text-align: center;'>";
            echo "<h4 style='margin: 0; color: #1976d2;'>" . $stats['today'] . "</h4>";
            echo "<p style='margin: 0.5rem 0 0; color: #666;'>আজকের অনুরোধ</p>";
            echo "</div>";
            
            echo "<div style='background: #f3e5f5; padding: 1rem; border-radius: 8px; text-align: center;'>";
            echo "<h4 style='margin: 0; color: #7b1fa2;'>" . $stats['week'] . "</h4>";
            echo "<p style='margin: 0.5rem 0 0; color: #666;'>এই সপ্তাহের অনুরোধ</p>";
            echo "</div>";
            
            echo "<div style='background: #e8f5e8; padding: 1rem; border-radius: 8px; text-align: center;'>";
            echo "<h4 style='margin: 0; color: #388e3c;'>" . $stats['successful_today'] . "</h4>";
            echo "<p style='margin: 0.5rem 0 0; color: #666;'>আজকের সফল রিসেট</p>";
            echo "</div>";
            
            echo "</div>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ পরিষ্কার করতে সমস্যা: " . $result['message'] . "</p>";
    }
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ ডাটাবেস এরর: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>স্বয়ংক্রিয় পরিষ্কার সেটআপ:</h3>";
echo "<p>প্রোডাকশনে, এই স্ক্রিপ্টটি cron job হিসেবে চালান:</p>";
echo "<code style='background: #f8f9fa; padding: 10px; display: block; border-radius: 4px;'>";
echo "# প্রতিদিন রাত ২টায় চালান<br>";
echo "0 2 * * * /usr/bin/php /path/to/your/project/cleanup_tokens.php";
echo "</code>";

echo "<p style='margin-top: 2rem;'>";
echo "<a href='forgot_password.php' style='color: #667eea;'>← পাসওয়ার্ড রিসেট পেজে ফিরে যান</a> | ";
echo "<a href='admin/dashboard.php' style='color: #667eea;'>অ্যাডমিন ড্যাশবোর্ড</a>";
echo "</p>";
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>টোকেন পরিষ্কার - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            max-width: 1000px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8f9fa;
            line-height: 1.6;
        }
        h2, h3, h4 {
            color: #333;
        }
        p {
            margin: 1rem 0;
        }
        ul {
            margin: 1rem 0;
            padding-left: 2rem;
        }
        a {
            color: #667eea;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        code {
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
</body>
</html>
