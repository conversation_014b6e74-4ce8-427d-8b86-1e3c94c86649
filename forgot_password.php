<?php
require_once 'config/session.php';
require_once 'config/database.php';
require_once 'config/email.php';

// If already logged in, redirect to profile
if (isset($_SESSION['customer_id'])) {
    redirect('profile.php');
}

$step = isset($_GET['step']) ? $_GET['step'] : 'request';
$token = isset($_GET['token']) ? sanitize_input($_GET['token']) : '';

if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'request_reset') {
        $login_input = sanitize_input($_POST['login_input']);
        
        if (empty($login_input)) {
            $error = 'ইমেইল বা মোবাইল নম্বর আবশ্যক।';
        } else {
            try {
                // Find customer by email or phone
                if (filter_var($login_input, FILTER_VALIDATE_EMAIL)) {
                    $stmt = $pdo->prepare("SELECT * FROM customers WHERE email = ? AND status = 'active'");
                } else {
                    $stmt = $pdo->prepare("SELECT * FROM customers WHERE phone = ? AND status = 'active'");
                }
                $stmt->execute([$login_input]);
                $customer = $stmt->fetch();
                
                if ($customer) {
                    // Generate reset token
                    $token = bin2hex(random_bytes(32));
                    $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));
                    
                    // Delete old tokens for this customer
                    $stmt = $pdo->prepare("DELETE FROM password_reset_tokens WHERE customer_id = ?");
                    $stmt->execute([$customer['id']]);
                    
                    // Insert new token
                    $stmt = $pdo->prepare("INSERT INTO password_reset_tokens (customer_id, token, expires_at) VALUES (?, ?, ?)");
                    $stmt->execute([$customer['id'], $token, $expires_at]);

                    // Send reset link via email or SMS
                    if ($customer['email']) {
                        $email_result = send_password_reset_email($customer, $token);
                        if ($email_result['success']) {
                            $success = "পাসওয়ার্ড রিসেট লিংক আপনার ইমেইলে পাঠানো হয়েছে: <strong>" . htmlspecialchars($customer['email']) . "</strong><br><br>
                                      <small style='color: #666;'>ইমেইল না পেলে স্প্যাম ফোল্ডার চেক করুন। লিংকটি ১ ঘন্টার জন্য বৈধ।</small><br><br>
                                      <div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 15px;'>
                                      <strong>ডেমো মোড:</strong> প্রোডাকশনে ইমেইল পাঠানো হবে। এখনের জন্য লিংক:<br>
                                      <a href='" . $email_result['demo_content']['reset_link'] . "' style='color: #667eea; font-weight: bold;'>পাসওয়ার্ড রিসেট করুন</a>
                                      </div>";
                        } else {
                            $error = 'ইমেইল পাঠাতে সমস্যা হয়েছে। পরে আবার চেষ্টা করুন।';
                        }
                    } else {
                        $sms_result = send_password_reset_sms($customer, $token);
                        if ($sms_result['success']) {
                            $success = "পাসওয়ার্ড রিসেট লিংক আপনার মোবাইলে SMS এ পাঠানো হয়েছে: <strong>" . htmlspecialchars($customer['phone']) . "</strong><br><br>
                                      <small style='color: #666;'>SMS না পেলে কিছুক্ষণ অপেক্ষা করুন। লিংকটি ১ ঘন্টার জন্য বৈধ।</small><br><br>
                                      <div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 15px;'>
                                      <strong>ডেমো মোড:</strong> প্রোডাকশনে SMS পাঠানো হবে। এখনের জন্য লিংক:<br>
                                      <a href='" . $sms_result['demo_content']['reset_link'] . "' style='color: #667eea; font-weight: bold;'>পাসওয়ার্ড রিসেট করুন</a>
                                      </div>";
                        } else {
                            $error = 'SMS পাঠাতে সমস্যা হয়েছে। পরে আবার চেষ্টা করুন।';
                        }
                    }
                    
                } else {
                    $error = 'এই ইমেইল বা মোবাইল নম্বর দিয়ে কোন অ্যাকাউন্ট পাওয়া যায়নি।';
                }
            } catch(PDOException $e) {
                $error = 'ডাটাবেস এরর: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'reset_password') {
        $token = sanitize_input($_POST['token']);
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        if (empty($new_password) || empty($confirm_password)) {
            $error = 'নতুন পাসওয়ার্ড এবং নিশ্চিতকরণ আবশ্যক।';
        } elseif ($new_password !== $confirm_password) {
            $error = 'পাসওয়ার্ড মিলছে না।';
        } elseif (strlen($new_password) < 6) {
            $error = 'পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে।';
        } else {
            try {
                // Verify token
                $stmt = $pdo->prepare("
                    SELECT prt.*, c.* 
                    FROM password_reset_tokens prt 
                    JOIN customers c ON prt.customer_id = c.id 
                    WHERE prt.token = ? AND prt.used = FALSE AND prt.expires_at > NOW()
                ");
                $stmt->execute([$token]);
                $reset_data = $stmt->fetch();
                
                if ($reset_data) {
                    // Update password
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE customers SET password = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$hashed_password, $reset_data['customer_id']]);
                    
                    // Mark token as used
                    $stmt = $pdo->prepare("UPDATE password_reset_tokens SET used = TRUE WHERE token = ?");
                    $stmt->execute([$token]);
                    
                    $success = 'পাসওয়ার্ড সফলভাবে পরিবর্তন করা হয়েছে। এখন আপনি নতুন পাসওয়ার্ড দিয়ে লগইন করতে পারেন।';
                    $step = 'success';
                } else {
                    $error = 'অবৈধ বা মেয়াদোত্তীর্ণ টোকেন। নতুন রিসেট অনুরোধ করুন।';
                }
            } catch(PDOException $e) {
                $error = 'ডাটাবেস এরর: ' . $e->getMessage();
            }
        }
    }
}

// Verify token for reset step
if ($step === 'reset' && !empty($token)) {
    try {
        $stmt = $pdo->prepare("
            SELECT prt.*, c.name, c.email, c.phone 
            FROM password_reset_tokens prt 
            JOIN customers c ON prt.customer_id = c.id 
            WHERE prt.token = ? AND prt.used = FALSE AND prt.expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $reset_data = $stmt->fetch();
        
        if (!$reset_data) {
            $error = 'অবৈধ বা মেয়াদোত্তীর্ণ টোকেন।';
            $step = 'request';
        }
    } catch(PDOException $e) {
        $error = 'ডাটাবেস এরর: ' . $e->getMessage();
        $step = 'request';
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পাসওয়ার্ড ভুলে গেছেন? - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .forgot-password-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem;
        }
        .forgot-password-card {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 500px;
        }
        .forgot-password-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .forgot-password-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        .forgot-password-header p {
            color: #666;
            margin-bottom: 0;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e9ecef;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 0.5rem;
        }
        .step.active .step-number {
            background: #667eea;
            color: white;
        }
        .step.completed .step-number {
            background: #28a745;
            color: white;
        }
        .step-text {
            font-size: 0.9rem;
            color: #666;
        }
        .step.active .step-text {
            color: #667eea;
            font-weight: 600;
        }
        .reset-info {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            border-left: 4px solid #667eea;
        }
        .reset-info h4 {
            color: #333;
            margin-bottom: 1rem;
        }
        .reset-info ul {
            margin: 0;
            padding-left: 1.5rem;
        }
        .reset-info li {
            margin-bottom: 0.5rem;
            color: #666;
        }
        .back-to-login {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }
        .success-icon {
            text-align: center;
            margin-bottom: 2rem;
        }
        .success-icon i {
            font-size: 4rem;
            color: #28a745;
        }
        @media (max-width: 768px) {
            .forgot-password-container {
                padding: 1rem;
            }
            .forgot-password-card {
                padding: 2rem;
            }
            .step-indicator {
                flex-direction: column;
                align-items: center;
            }
            .step {
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="forgot-password-container">
        <div class="forgot-password-card">
            <div class="forgot-password-header">
                <h1><i class="fas fa-key"></i> পাসওয়ার্ড পুনরুদ্ধার</h1>
                <p>আপনার অ্যাকাউন্টের পাসওয়ার্ড রিসেট করুন</p>
            </div>
            
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step <?php echo $step === 'request' ? 'active' : ($step !== 'request' ? 'completed' : ''); ?>">
                    <div class="step-number">1</div>
                    <div class="step-text">তথ্য দিন</div>
                </div>
                <div class="step <?php echo $step === 'reset' ? 'active' : ($step === 'success' ? 'completed' : ''); ?>">
                    <div class="step-number">2</div>
                    <div class="step-text">নতুন পাসওয়ার্ড</div>
                </div>
                <div class="step <?php echo $step === 'success' ? 'active' : ''; ?>">
                    <div class="step-number">3</div>
                    <div class="step-text">সম্পন্ন</div>
                </div>
            </div>
            
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step === 'request'): ?>
                <!-- Step 1: Request Reset -->
                <div class="reset-info">
                    <h4>পাসওয়ার্ড রিসেট প্রক্রিয়া:</h4>
                    <ul>
                        <li>আপনার ইমেইল বা মোবাইল নম্বর দিন</li>
                        <li>একটি রিসেট লিংক তৈরি হবে</li>
                        <li>লিংকে ক্লিক করে নতুন পাসওয়ার্ড সেট করুন</li>
                        <li>রিসেট লিংক ১ ঘন্টার জন্য বৈধ থাকবে</li>
                    </ul>
                </div>
                
                <form method="POST" id="requestResetForm">
                    <input type="hidden" name="action" value="request_reset">
                    
                    <div class="form-group">
                        <label for="login_input">ইমেইল বা মোবাইল নম্বর</label>
                        <input type="text" name="login_input" id="login_input" class="form-control" 
                               placeholder="<EMAIL> বা 01XXXXXXXXX" required>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-paper-plane"></i> রিসেট লিংক পাঠান
                        </button>
                    </div>
                </form>
                
            <?php elseif ($step === 'reset'): ?>
                <!-- Step 2: Reset Password -->
                <div class="reset-info">
                    <h4>নতুন পাসওয়ার্ড সেট করুন:</h4>
                    <p><strong>অ্যাকাউন্ট:</strong> <?php echo htmlspecialchars($reset_data['name']); ?></p>
                    <p><strong>যোগাযোগ:</strong> <?php echo htmlspecialchars($reset_data['email'] ?: $reset_data['phone']); ?></p>
                </div>
                
                <form method="POST" id="resetPasswordForm">
                    <input type="hidden" name="action" value="reset_password">
                    <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
                    
                    <div class="form-group">
                        <label for="new_password">নতুন পাসওয়ার্ড</label>
                        <input type="password" name="new_password" id="new_password" class="form-control" 
                               placeholder="কমপক্ষে ৬ অক্ষর" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">পাসওয়ার্ড নিশ্চিত করুন</label>
                        <input type="password" name="confirm_password" id="confirm_password" class="form-control" 
                               placeholder="পাসওয়ার্ড পুনরায় টাইপ করুন" required>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-save"></i> পাসওয়ার্ড পরিবর্তন করুন
                        </button>
                    </div>
                </form>
                
            <?php elseif ($step === 'success'): ?>
                <!-- Step 3: Success -->
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                
                <div style="text-align: center;">
                    <h3 style="color: #28a745; margin-bottom: 1rem;">পাসওয়ার্ড সফলভাবে পরিবর্তন হয়েছে!</h3>
                    <p style="margin-bottom: 2rem;">আপনি এখন নতুন পাসওয়ার্ড দিয়ে লগইন করতে পারেন।</p>
                    
                    <a href="login.php" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> লগইন করুন
                    </a>
                </div>
            <?php endif; ?>
            
            <div class="back-to-login">
                <p>
                    <a href="login.php" style="color: #667eea; text-decoration: none;">
                        <i class="fas fa-arrow-left"></i> লগইন পেজে ফিরে যান
                    </a>
                </p>
                <p>
                    <a href="index.php" style="color: #666; text-decoration: none;">
                        <i class="fas fa-home"></i> হোম পেজে যান
                    </a>
                </p>
            </div>
        </div>
    </div>

    <script src="assets/js/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            // Password confirmation validation
            $('#confirm_password').on('input', function() {
                const newPassword = $('#new_password').val();
                const confirmPassword = $(this).val();
                
                if (newPassword !== confirmPassword) {
                    this.setCustomValidity('পাসওয়ার্ড মিলছে না');
                } else {
                    this.setCustomValidity('');
                }
            });
            
            // Form validation
            $('#requestResetForm').on('submit', function(e) {
                const loginInput = $('#login_input').val().trim();
                
                if (!loginInput) {
                    e.preventDefault();
                    alert('ইমেইল বা মোবাইল নম্বর আবশ্যক।');
                    return false;
                }
            });
            
            $('#resetPasswordForm').on('submit', function(e) {
                const newPassword = $('#new_password').val();
                const confirmPassword = $('#confirm_password').val();
                
                if (newPassword !== confirmPassword) {
                    e.preventDefault();
                    alert('পাসওয়ার্ড মিলছে না।');
                    return false;
                }
                
                if (newPassword.length < 6) {
                    e.preventDefault();
                    alert('পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে।');
                    return false;
                }
            });
        });
    </script>
</body>
</html>
