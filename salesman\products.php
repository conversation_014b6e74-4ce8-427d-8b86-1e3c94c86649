<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if salesman is logged in
if (!isset($_SESSION['salesman_id'])) {
    redirect('login.php');
}

// Get filter parameters
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : '';
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$stock_filter = isset($_GET['stock']) ? sanitize_input($_GET['stock']) : '';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query
$sql = "SELECT p.*, c.name as category_name FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id WHERE 1=1";
$params = [];

// Add filters
if ($category_filter) {
    $sql .= " AND p.category_id = ?";
    $params[] = $category_filter;
}

if ($status_filter) {
    $sql .= " AND p.status = ?";
    $params[] = $status_filter;
}

if ($search) {
    $sql .= " AND (p.name LIKE ? OR p.description LIKE ?)";
    $searchTerm = "%$search%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if ($stock_filter) {
    switch ($stock_filter) {
        case 'low':
            $sql .= " AND p.stock_quantity <= 5";
            break;
        case 'out':
            $sql .= " AND p.stock_quantity = 0";
            break;
        case 'available':
            $sql .= " AND p.stock_quantity > 0";
            break;
    }
}

$sql .= " ORDER BY p.name ASC LIMIT $limit OFFSET $offset";

try {
    // Get products
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll();
    
    // Get total count for pagination
    $countSql = str_replace("SELECT p.*, c.name as category_name", "SELECT COUNT(*)", $sql);
    $countSql = str_replace("ORDER BY p.name ASC LIMIT $limit OFFSET $offset", "", $countSql);
    
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $totalProducts = $countStmt->fetchColumn();
    $totalPages = ceil($totalProducts / $limit);
    
    // Get categories for filter
    $stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name ASC");
    $categories = $stmt->fetchAll();
    
    // Get statistics
    $stmt = $pdo->query("SELECT 
                        COUNT(*) as total_products,
                        SUM(CASE WHEN stock_quantity <= 5 THEN 1 ELSE 0 END) as low_stock,
                        SUM(CASE WHEN stock_quantity = 0 THEN 1 ELSE 0 END) as out_of_stock,
                        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_products
                        FROM products");
    $stats = $stmt->fetch();
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    $products = [];
    $categories = [];
    $totalProducts = 0;
    $totalPages = 0;
    $stats = ['total_products' => 0, 'low_stock' => 0, 'out_of_stock' => 0, 'active_products' => 0];
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পণ্য তালিকা - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo h1 {
            font-size: 1.5rem;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .nav-menu {
            background: white;
            padding: 1rem 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .nav-menu ul {
            list-style: none;
            display: flex;
            gap: 2rem;
        }
        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .nav-menu a:hover, .nav-menu a.active {
            background: #667eea;
            color: white;
        }
        .main-content {
            padding: 2rem 0;
        }
        .page-header {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .page-header h2 {
            margin-bottom: 1rem;
            color: #333;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .stat-card h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #333;
        }
        .stat-card p {
            color: #666;
            font-size: 0.9rem;
        }
        .filters {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: background-color 0.3s ease;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
        }
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
        }
        .product-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        .product-card:hover {
            transform: translateY(-5px);
        }
        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .product-info {
            padding: 1.5rem;
        }
        .product-name {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #333;
        }
        .product-category {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        .product-price {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        .current-price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
        }
        .original-price {
            font-size: 0.9rem;
            color: #999;
            text-decoration: line-through;
        }
        .product-stock {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        .stock-info {
            font-size: 0.9rem;
        }
        .stock-available {
            color: #28a745;
        }
        .stock-low {
            color: #ffc107;
        }
        .stock-out {
            color: #dc3545;
        }
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .badge-success {
            background: #d4edda;
            color: #155724;
        }
        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }
        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        .pagination a,
        .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        .pagination a:hover {
            background: #667eea;
            color: white;
        }
        .pagination .current {
            background: #667eea;
            color: white;
        }
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            .nav-menu ul {
                flex-wrap: wrap;
                gap: 1rem;
            }
            .filters-grid {
                grid-template-columns: 1fr;
            }
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-cash-register"></i> POS সিস্টেম</h1>
                </div>
                <div class="user-info">
                    <span><i class="fas fa-user"></i> <?php echo $_SESSION['salesman_name']; ?></span>
                    <a href="dashboard.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                    </a>
                    <a href="logout.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-sign-out-alt"></i> লগআউট
                    </a>
                </div>
            </div>
        </div>
    </header>

    <nav class="nav-menu">
        <div class="container">
            <ul>
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="pos.php"><i class="fas fa-cash-register"></i> নতুন বিক্রয়</a></li>
                <li><a href="sales.php"><i class="fas fa-list"></i> বিক্রয় তালিকা</a></li>
                <li><a href="products.php" class="active"><i class="fas fa-box"></i> পণ্য তালিকা</a></li>
                <li><a href="reports.php"><i class="fas fa-chart-bar"></i> রিপোর্ট</a></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="page-header">
                <h2><i class="fas fa-box"></i> পণ্য তালিকা</h2>
                <p>দোকানের সকল পণ্যের তালিকা এবং স্টক তথ্য</p>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-box" style="color: #667eea;"></i>
                    <h3><?php echo number_format($stats['total_products']); ?></h3>
                    <p>মোট পণ্য</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-eye" style="color: #2ed573;"></i>
                    <h3><?php echo number_format($stats['active_products']); ?></h3>
                    <p>সক্রিয় পণ্য</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-exclamation-triangle" style="color: #ffa502;"></i>
                    <h3><?php echo number_format($stats['low_stock']); ?></h3>
                    <p>কম স্টক</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-times-circle" style="color: #ff4757;"></i>
                    <h3><?php echo number_format($stats['out_of_stock']); ?></h3>
                    <p>স্টক শেষ</p>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters">
                <form method="GET">
                    <div class="filters-grid">
                        <div class="form-group">
                            <label>ক্যাটেগরি</label>
                            <select name="category" class="form-control">
                                <option value="">সব ক্যাটেগরি</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>স্ট্যাটাস</label>
                            <select name="status" class="form-control">
                                <option value="">সব স্ট্যাটাস</option>
                                <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>সক্রিয়</option>
                                <option value="inactive" <?php echo $status_filter == 'inactive' ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>স্টক</label>
                            <select name="stock" class="form-control">
                                <option value="">সব স্টক</option>
                                <option value="available" <?php echo $stock_filter == 'available' ? 'selected' : ''; ?>>উপলব্ধ</option>
                                <option value="low" <?php echo $stock_filter == 'low' ? 'selected' : ''; ?>>কম স্টক</option>
                                <option value="out" <?php echo $stock_filter == 'out' ? 'selected' : ''; ?>>স্টক শেষ</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>খুঁজুন</label>
                            <input type="text" name="search" class="form-control" placeholder="পণ্যের নাম..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> ফিল্টার করুন
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Products Grid -->
            <?php if (empty($products)): ?>
                <div style="background: white; padding: 3rem; text-align: center; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <i class="fas fa-box fa-3x" style="color: #ddd; margin-bottom: 1rem;"></i>
                    <h3>কোন পণ্য পাওয়া যায়নি</h3>
                    <p>নির্বাচিত ফিল্টার অনুযায়ী কোন পণ্য খুঁজে পাওয়া যায়নি।</p>
                </div>
            <?php else: ?>
                <div class="products-grid">
                    <?php foreach ($products as $product): ?>
                        <div class="product-card">
                            <div class="product-image">
                                <?php if ($product['image']): ?>
                                    <img src="../uploads/products/<?php echo $product['image']; ?>"
                                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                                         style="width: 100%; height: 200px; object-fit: cover;">
                                <?php else: ?>
                                    <i class="fas fa-box fa-3x" style="color: #ddd;"></i>
                                <?php endif; ?>
                            </div>

                            <div class="product-info">
                                <div class="product-name"><?php echo htmlspecialchars($product['name']); ?></div>
                                <div class="product-category">
                                    <i class="fas fa-tag"></i> <?php echo htmlspecialchars($product['category_name'] ?: 'অবিভাগীকৃত'); ?>
                                </div>

                                <div class="product-price">
                                    <span class="current-price">
                                        ৳<?php echo number_format($product['discount_price'] ?: $product['price'], 2); ?>
                                    </span>
                                    <?php if ($product['discount_price'] && $product['discount_price'] < $product['price']): ?>
                                        <span class="original-price">৳<?php echo number_format($product['price'], 2); ?></span>
                                    <?php endif; ?>
                                </div>

                                <div class="product-stock">
                                    <span class="stock-info">
                                        <i class="fas fa-cubes"></i> স্টক:
                                        <span class="<?php
                                            if ($product['stock_quantity'] == 0) echo 'stock-out';
                                            elseif ($product['stock_quantity'] <= 5) echo 'stock-low';
                                            else echo 'stock-available';
                                        ?>">
                                            <?php echo $product['stock_quantity']; ?>
                                        </span>
                                    </span>

                                    <span class="badge <?php
                                        if ($product['status'] == 'active') echo 'badge-success';
                                        else echo 'badge-warning';
                                    ?>">
                                        <?php echo $product['status'] == 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                    </span>
                                </div>

                                <?php if ($product['stock_quantity'] > 0 && $product['status'] == 'active'): ?>
                                    <button onclick="addToCart(<?php echo htmlspecialchars(json_encode($product)); ?>)"
                                            class="btn btn-primary" style="width: 100%;">
                                        <i class="fas fa-cart-plus"></i> কার্টে যোগ করুন
                                    </button>
                                <?php else: ?>
                                    <button class="btn" style="width: 100%; background: #6c757d; color: white;" disabled>
                                        <i class="fas fa-ban"></i>
                                        <?php echo $product['stock_quantity'] == 0 ? 'স্টক নেই' : 'অনুপলব্ধ'; ?>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&category=<?php echo $category_filter; ?>&status=<?php echo $status_filter; ?>&stock=<?php echo $stock_filter; ?>&search=<?php echo urlencode($search); ?>">
                                <i class="fas fa-chevron-left"></i> পূর্ববর্তী
                            </a>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <?php if ($i == $page): ?>
                                <span class="current"><?php echo $i; ?></span>
                            <?php else: ?>
                                <a href="?page=<?php echo $i; ?>&category=<?php echo $category_filter; ?>&status=<?php echo $status_filter; ?>&stock=<?php echo $stock_filter; ?>&search=<?php echo urlencode($search); ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endif; ?>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&category=<?php echo $category_filter; ?>&status=<?php echo $status_filter; ?>&stock=<?php echo $stock_filter; ?>&search=<?php echo urlencode($search); ?>">
                                পরবর্তী <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </main>

    <script>
        function addToCart(product) {
            // Store product in localStorage for POS system
            let cart = JSON.parse(localStorage.getItem('pos_cart')) || [];

            const existingItem = cart.find(item => item.id === product.id);

            if (existingItem) {
                if (existingItem.quantity < product.stock_quantity) {
                    existingItem.quantity++;
                    alert(`${product.name} কার্টে যোগ করা হয়েছে! (পরিমাণ: ${existingItem.quantity})`);
                } else {
                    alert('স্টক সীমা অতিক্রম করা যাবে না!');
                    return;
                }
            } else {
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: parseFloat(product.discount_price || product.price),
                    quantity: 1,
                    stock: product.stock_quantity
                });
                alert(`${product.name} কার্টে যোগ করা হয়েছে!`);
            }

            localStorage.setItem('pos_cart', JSON.stringify(cart));

            // Ask if user wants to go to POS
            if (confirm('POS সিস্টেমে যেতে চান?')) {
                window.location.href = 'pos.php';
            }
        }
    </script>
</body>
</html>
