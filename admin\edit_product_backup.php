<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

try {
    require_once '../includes/error_handler.php';
    require_once '../config/session.php';
    require_once '../config/database.php';
    require_once '../includes/image_handler.php';
} catch (Error $e) {
    die("Include Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
} catch (Exception $e) {
    die("Include Exception: " . $e->getMessage());
}

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Get product ID
$productId = $_GET['id'] ?? null;
if (!$productId) {
    header('Location: products.php');
    exit;
}

// Get product details
$stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.id = ?");
$stmt->execute([$productId]);
$product = $stmt->fetch();

if (!$product) {
    header('Location: products.php');
    exit;
}

// Get categories
$stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
$categories = $stmt->fetchAll();

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'update_product') {
            $name = trim(safe_post('name'));
            $description = trim(safe_post('description'));
            $price = floatval(safe_post('price', 0));
            $stock_quantity = intval(safe_post('stock_quantity', 0));
            $category_id = intval(safe_post('category_id', 0));
            $sku = trim(safe_post('sku'));
            $status = safe_post('status', 'active');
            
            // Validation
            if (empty($name) || empty($price) || empty($category_id)) {
                throw new Exception('নাম, দাম এবং ক্যাটাগরি আবশ্যক।');
            }
            
            if ($price <= 0) {
                throw new Exception('দাম অবশ্যই ০ এর চেয়ে বেশি হতে হবে।');
            }
            
            if ($stock_quantity < 0) {
                throw new Exception('স্টক পরিমাণ ০ বা তার বেশি হতে হবে।');
            }
            
            // Check if SKU already exists (excluding current product)
            if (!empty($sku)) {
                $stmt = $pdo->prepare("SELECT id FROM products WHERE sku = ? AND id != ?");
                $stmt->execute([$sku, $productId]);
                if ($stmt->fetch()) {
                    throw new Exception('এই SKU ইতিমধ্যে অন্য পণ্যে ব্যবহৃত হয়েছে।');
                }
            }
            
            // Handle image upload
            $imageName = $product['image']; // Keep existing image by default
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $errors = ImageHandler::validateImage($_FILES['image']);
                if (!empty($errors)) {
                    throw new Exception('ছবি আপলোড এরর: ' . implode(', ', $errors));
                }
                
                $newImageName = ImageHandler::generateUniqueFilename($_FILES['image']['name'], 'product_');
                $destination = '../uploads/products/' . $newImageName;
                
                if (!ImageHandler::moveUploadedFile($_FILES['image'], $destination)) {
                    throw new Exception('ছবি সংরক্ষণে সমস্যা হয়েছে।');
                }
                
                // Resize image if needed
                $extension = strtolower(pathinfo($newImageName, PATHINFO_EXTENSION));
                if ($extension !== 'svg') {
                    $resizedPath = '../uploads/products/resized_' . $newImageName;
                    if (ImageHandler::resizeImage($destination, $resizedPath, 800, 600)) {
                        unlink($destination);
                        $newImageName = 'resized_' . $newImageName;
                    }
                }
                
                // Delete old image if exists
                if ($product['image'] && file_exists('../uploads/products/' . $product['image'])) {
                    unlink('../uploads/products/' . $product['image']);
                }
                
                $imageName = $newImageName;
            }
            
            // Update product
            $stmt = $pdo->prepare("UPDATE products SET name = ?, description = ?, price = ?, stock_quantity = ?, category_id = ?, sku = ?, image = ?, status = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$name, $description, $price, $stock_quantity, $category_id, $sku, $imageName, $status, $productId]);
            
            $message = 'পণ্য সফলভাবে আপডেট করা হয়েছে!';
            $messageType = 'success';
            
            // Refresh product data
            $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.id = ?");
            $stmt->execute([$productId]);
            $product = $stmt->fetch();
            
        } elseif ($_POST['action'] === 'delete_image') {
            // Delete current image
            if ($product['image'] && file_exists('../uploads/products/' . $product['image'])) {
                unlink('../uploads/products/' . $product['image']);
            }
            
            $stmt = $pdo->prepare("UPDATE products SET image = NULL WHERE id = ?");
            $stmt->execute([$productId]);
            
            $message = 'ছবি সফলভাবে মুছে ফেলা হয়েছে!';
            $messageType = 'success';
            
            // Refresh product data
            $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.id = ?");
            $stmt->execute([$productId]);
            $product = $stmt->fetch();
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পণ্য সম্পাদনা - <?php echo htmlspecialchars($product['name']); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-left h1 {
            font-size: 2rem;
            margin-bottom: 5px;
        }
        
        .header-left p {
            opacity: 0.9;
        }
        
        .header-actions {
            display: flex;
            gap: 15px;
        }
        
        .header-btn {
            padding: 10px 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .header-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 30px;
            padding: 30px;
        }
        
        .form-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .info-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
        }
        
        .info-card h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .current-image {
            width: 100%;
            max-width: 250px;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        
        .image-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 20px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .required {
            color: #dc3545;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .file-upload-area {
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .file-upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        
        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
        
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e0e0e0;
        }
        
        .message {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .image-preview {
            margin-top: 15px;
            text-align: center;
        }
        
        .preview-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .header-actions {
                justify-content: center;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <h1><i class="fas fa-edit"></i> পণ্য সম্পাদনা</h1>
                <p><?php echo htmlspecialchars($product['name']); ?></p>
            </div>
            <div class="header-actions">
                <a href="products.php" class="header-btn">
                    <i class="fas fa-arrow-left"></i> পণ্য তালিকা
                </a>
                <a href="category_products_overview.php" class="header-btn">
                    <i class="fas fa-layer-group"></i> ক্যাটাগরি ভিউ
                </a>
                <a href="add_product_by_category.php" class="header-btn">
                    <i class="fas fa-plus"></i> নতুন পণ্য
                </a>
            </div>
        </div>

        <div class="main-content">
            <!-- Main Form -->
            <div class="form-section">
                <?php if ($message): ?>
                    <div class="message <?php echo $messageType; ?>">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data" id="editProductForm">
                    <input type="hidden" name="action" value="update_product">

                    <!-- Basic Information -->
                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #333; margin-bottom: 20px;"><i class="fas fa-info-circle"></i> মৌলিক তথ্য</h3>
                        
                        <div class="form-group">
                            <label for="name">পণ্যের নাম <span class="required">*</span></label>
                            <input type="text" id="name" name="name" class="form-control" required value="<?php echo htmlspecialchars($product['name']); ?>">
                        </div>

                        <div class="form-group">
                            <label for="sku">SKU (স্টক কিপিং ইউনিট)</label>
                            <input type="text" id="sku" name="sku" class="form-control" value="<?php echo htmlspecialchars($product['sku']); ?>">
                        </div>

                        <div class="form-group">
                            <label for="description">বিবরণ</label>
                            <textarea id="description" name="description" class="form-control" rows="4"><?php echo htmlspecialchars($product['description']); ?></textarea>
                        </div>
                    </div>

                    <!-- Category & Status -->
                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #333; margin-bottom: 20px;"><i class="fas fa-tags"></i> ক্যাটাগরি ও স্ট্যাটাস</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="category_id">ক্যাটাগরি <span class="required">*</span></label>
                                <select id="category_id" name="category_id" class="form-control" required>
                                    <option value="">ক্যাটাগরি নির্বাচন করুন</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" <?php echo ($category['id'] == $product['category_id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="status">স্ট্যাটাস</label>
                                <select id="status" name="status" class="form-control">
                                    <option value="active" <?php echo ($product['status'] == 'active') ? 'selected' : ''; ?>>সক্রিয়</option>
                                    <option value="inactive" <?php echo ($product['status'] == 'inactive') ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing & Stock -->
                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #333; margin-bottom: 20px;"><i class="fas fa-dollar-sign"></i> দাম ও স্টক</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="price">দাম (৳) <span class="required">*</span></label>
                                <input type="number" id="price" name="price" class="form-control" step="0.01" min="0" required value="<?php echo $product['price']; ?>">
                            </div>

                            <div class="form-group">
                                <label for="stock_quantity">স্টক পরিমাণ</label>
                                <input type="number" id="stock_quantity" name="stock_quantity" class="form-control" min="0" value="<?php echo $product['stock_quantity']; ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Image Upload -->
                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #333; margin-bottom: 20px;"><i class="fas fa-image"></i> নতুন ছবি আপলোড (ঐচ্ছিক)</h3>
                        
                        <div class="file-upload-area" onclick="document.getElementById('image').click()">
                            <input type="file" id="image" name="image" class="file-input" accept="image/*,.svg" onchange="previewImage(this)">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div>নতুন ছবি আপলোড করতে ক্লিক করুন</div>
                            <small style="color: #666;">বর্তমান ছবি পরিবর্তন করতে চাইলে</small>
                        </div>

                        <div class="image-preview" id="imagePreview" style="display: none;">
                            <img id="previewImg" class="preview-image" alt="Preview">
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <a href="products.php" class="btn btn-secondary">
                            <i class="fas fa-times"></i> বাতিল
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> পরিবর্তন সংরক্ষণ
                        </button>
                    </div>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Current Image -->
                <div class="info-card">
                    <h3><i class="fas fa-image"></i> বর্তমান ছবি</h3>
                    <?php if ($product['image']): ?>
                        <div style="text-align: center;">
                            <?php echo ImageHandler::getImageHTML($product['image'], $product['name'], 'current-image', 'width: 100%; max-width: 250px; border-radius: 10px;', $product['id']); ?>
                            <div class="image-actions">
                                <form method="POST" style="display: inline;" onsubmit="return confirm('আপনি কি নিশ্চিত যে এই ছবি মুছে ফেলতে চান?')">
                                    <input type="hidden" name="action" value="delete_image">
                                    <button type="submit" class="btn-sm btn-danger">
                                        <i class="fas fa-trash"></i> ছবি মুছুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php else: ?>
                        <div style="text-align: center; color: #666;">
                            <i class="fas fa-image" style="font-size: 48px; opacity: 0.3; margin-bottom: 10px;"></i>
                            <p>কোন ছবি নেই</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Product Info -->
                <div class="info-card">
                    <h3><i class="fas fa-info-circle"></i> পণ্যের তথ্য</h3>
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <p><strong>ID:</strong> #<?php echo $product['id']; ?></p>
                        <p><strong>ক্যাটাগরি:</strong> <?php echo htmlspecialchars($product['category_name'] ?? 'N/A'); ?></p>
                        <p><strong>স্ট্যাটাস:</strong> 
                            <span class="status-badge status-<?php echo $product['status']; ?>">
                                <?php echo $product['status'] == 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                            </span>
                        </p>
                        <p><strong>তৈরি:</strong> <?php echo date('d/m/Y', strtotime($product['created_at'])); ?></p>
                        <?php if ($product['updated_at']): ?>
                            <p><strong>আপডেট:</strong> <?php echo date('d/m/Y', strtotime($product['updated_at'])); ?></p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="info-card">
                    <h3><i class="fas fa-bolt"></i> দ্রুত অ্যাকশন</h3>
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <a href="../products.php?category=<?php echo $product['category_id']; ?>" class="btn btn-secondary" style="text-align: center; text-decoration: none;">
                            <i class="fas fa-eye"></i> ফ্রন্টএন্ডে দেখুন
                        </a>
                        <a href="upload_product_image.php" class="btn btn-secondary" style="text-align: center; text-decoration: none;">
                            <i class="fas fa-images"></i> ছবি ম্যানেজার
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Image preview
        function previewImage(input) {
            const file = input.files[0];
            const preview = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
            }
        }

        // Form validation
        document.getElementById('editProductForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const price = document.getElementById('price').value;
            const categoryId = document.getElementById('category_id').value;
            
            if (!name) {
                e.preventDefault();
                alert('পণ্যের নাম আবশ্যক।');
                document.getElementById('name').focus();
                return;
            }
            
            if (!price || parseFloat(price) <= 0) {
                e.preventDefault();
                alert('সঠিক দাম দিন।');
                document.getElementById('price').focus();
                return;
            }
            
            if (!categoryId) {
                e.preventDefault();
                alert('ক্যাটাগরি নির্বাচন করুন।');
                document.getElementById('category_id').focus();
                return;
            }
        });

        // Auto-hide success messages
        setTimeout(() => {
            const successMessage = document.querySelector('.message.success');
            if (successMessage) {
                successMessage.style.opacity = '0';
                setTimeout(() => {
                    successMessage.style.display = 'none';
                }, 300);
            }
        }, 3000);
    </script>
</body>
</html>
