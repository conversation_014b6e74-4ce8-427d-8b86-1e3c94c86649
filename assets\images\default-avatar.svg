<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="100" fill="url(#avatarGradient)"/>
  
  <!-- User Icon -->
  <g fill="white" opacity="0.9">
    <!-- Head -->
    <circle cx="100" cy="75" r="25"/>
    
    <!-- Body -->
    <path d="M100 110 C85 110, 70 120, 60 140 L60 180 L140 180 L140 140 C130 120, 115 110, 100 110 Z"/>
  </g>
  
  <!-- Border -->
  <circle cx="100" cy="100" r="98" fill="none" stroke="white" stroke-width="2" opacity="0.3"/>
</svg>
