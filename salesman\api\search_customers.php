<?php
header('Content-Type: application/json');
require_once '../../config/session.php';
require_once '../../config/database.php';

// Check if salesman is logged in
if (!isset($_SESSION['salesman_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    $search = isset($_GET['q']) ? sanitize_input($_GET['q']) : '';
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    
    if (empty($search)) {
        echo json_encode(['success' => true, 'data' => []]);
        exit;
    }
    
    // Search customers by name or phone
    $sql = "SELECT id, name, phone, email, address, city 
            FROM customers 
            WHERE status = 'active' 
            AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)
            ORDER BY name ASC 
            LIMIT ?";
    
    $searchTerm = "%$search%";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $limit]);
    $customers = $stmt->fetchAll();
    
    // Get customer statistics for each customer
    foreach ($customers as &$customer) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as total_orders, 
                              SUM(final_amount) as total_spent,
                              MAX(created_at) as last_order
                              FROM pos_sales 
                              WHERE customer_id = ?");
        $stmt->execute([$customer['id']]);
        $stats = $stmt->fetch();
        
        $customer['total_orders'] = (int)$stats['total_orders'];
        $customer['total_spent'] = (float)$stats['total_spent'];
        $customer['last_order'] = $stats['last_order'];
    }
    
    echo json_encode([
        'success' => true,
        'data' => $customers
    ]);
    
} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'ডাটাবেস এরর: ' . $e->getMessage()
    ]);
} catch(Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'সার্ভার এরর: ' . $e->getMessage()
    ]);
}
?>
