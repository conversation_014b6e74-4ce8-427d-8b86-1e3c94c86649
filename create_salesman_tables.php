<?php
require_once 'config/database.php';

try {
    // Create salesman table
    $sql = "CREATE TABLE IF NOT EXISTS salesman (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        phone VARCHAR(20),
        password VARCHAR(255) NOT NULL,
        employee_id VARCHAR(50) UNIQUE,
        commission_rate DECIMAL(5,2) DEFAULT 0.00,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "✅ Salesman table created successfully<br>";

    // Create pos_sales table
    $sql = "CREATE TABLE IF NOT EXISTS pos_sales (
        id INT PRIMARY KEY AUTO_INCREMENT,
        sale_number VARCHAR(50) UNIQUE NOT NULL,
        salesman_id INT,
        customer_name VARCHAR(100),
        customer_phone VARCHAR(20),
        total_amount DECIMAL(10,2) NOT NULL,
        discount_amount DECIMAL(10,2) DEFAULT 0.00,
        final_amount DECIMAL(10,2) NOT NULL,
        payment_method ENUM('cash', 'card', 'mobile_banking') DEFAULT 'cash',
        payment_status ENUM('paid', 'partial', 'pending') DEFAULT 'paid',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (salesman_id) REFERENCES salesman(id)
    )";
    $pdo->exec($sql);
    echo "✅ POS Sales table created successfully<br>";

    // Create pos_sale_items table
    $sql = "CREATE TABLE IF NOT EXISTS pos_sale_items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        sale_id INT NOT NULL,
        product_id INT NOT NULL,
        quantity INT NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (sale_id) REFERENCES pos_sales(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id)
    )";
    $pdo->exec($sql);
    echo "✅ POS Sale Items table created successfully<br>";

    // Insert default salesman (password: salesman123)
    $password = password_hash('salesman123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO salesman (name, email, phone, password, employee_id, commission_rate) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute(['রহিম উদ্দিন', '<EMAIL>', '01700000000', $password, 'EMP001', 5.00]);
    echo "✅ Default salesman created successfully<br>";

    echo "<br><strong>🎉 Salesman system setup completed!</strong><br>";
    echo "<br><strong>Login Details:</strong><br>";
    echo "URL: <a href='salesman/login.php'>salesman/login.php</a><br>";
    echo "Email: <EMAIL><br>";
    echo "Password: salesman123<br>";

} catch(PDOException $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
