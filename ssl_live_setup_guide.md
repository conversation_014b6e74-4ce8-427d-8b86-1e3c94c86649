# SSL Commerz Live Setup Guide

## 🚀 Production Environment Setup

### Step 1: SSL Commerz Account Creation
1. **Visit:** https://sslcommerz.com/
2. **Click:** "Merchant Registration"
3. **Fill:** Business information
4. **Submit:** Required documents

### Step 2: Required Documents
- **Trade License** (Business registration)
- **National ID** (Owner/Authorized person)
- **Bank Statement** (Last 3 months)
- **Utility Bill** (Business address proof)
- **Website URL** (Your e-commerce site)

### Step 3: Account Verification
- **Timeline:** 1-3 business days
- **Contact:** +88-*************
- **Email:** <EMAIL>
- **Status Check:** Merchant portal

### Step 4: Live Credentials
After approval, you'll receive:
- **Store ID:** Your unique merchant ID
- **Store Password:** API access password
- **Merchant Portal:** Dashboard access

### Step 5: Configure Live Mode
1. **Admin Panel:** Go to SSL Settings
2. **Store ID:** Enter your live Store ID
3. **Store Password:** Enter your live password
4. **Sandbox Mode:** ❌ Uncheck this
5. **SSL Commerz সক্রিয় করুন:** ✅ Check this
6. **Save Settings**

### Step 6: Test Live Environment
1. **Small Amount Test:** ৳10-50 test transaction
2. **Verify Payment:** Check merchant portal
3. **Refund Test:** Test refund process
4. **Go Live:** Start accepting real payments

## 💰 Pricing & Charges

### Transaction Fees
- **Domestic Cards:** 2.9% + VAT
- **International Cards:** 3.9% + VAT
- **Mobile Banking:** 1.85% + VAT
- **Net Banking:** 1.85% + VAT

### Settlement
- **Frequency:** Daily/Weekly (configurable)
- **Bank Transfer:** Direct to your account
- **Settlement Report:** Available in portal

## 🔒 Security Features

### SSL Commerz Provides
- **PCI DSS Compliance**
- **256-bit SSL Encryption**
- **Fraud Detection**
- **3D Secure Authentication**
- **Real-time Transaction Monitoring**

### Your Responsibilities
- **Secure Server:** HTTPS required
- **Regular Updates:** Keep system updated
- **Access Control:** Limit admin access
- **Backup:** Regular database backups

## 📞 Support & Contact

### Technical Support
- **Phone:** +88-*************
- **Email:** <EMAIL>
- **Hours:** 9 AM - 6 PM (Sat-Thu)

### Developer Resources
- **Documentation:** https://developer.sslcommerz.com/
- **API Reference:** Complete integration guide
- **Sample Code:** PHP, Java, .NET examples
- **Postman Collection:** API testing

## ⚠️ Important Notes

### Before Going Live
1. **Test Thoroughly:** All payment scenarios
2. **Backup Database:** Before switching modes
3. **Inform Customers:** Payment method availability
4. **Monitor Closely:** First few transactions

### Common Issues
1. **Credentials Error:** Double-check Store ID/Password
2. **SSL Certificate:** Ensure HTTPS is working
3. **Callback URLs:** Verify success/fail URLs
4. **Firewall:** Allow SSL Commerz IPs

### Best Practices
1. **Transaction Logging:** Keep detailed logs
2. **Error Handling:** User-friendly error messages
3. **Timeout Handling:** Handle network timeouts
4. **Reconciliation:** Daily transaction matching

## 🎯 Go-Live Checklist

- [ ] SSL Commerz account approved
- [ ] Live credentials received
- [ ] Sandbox mode disabled
- [ ] Live credentials configured
- [ ] Test transaction successful
- [ ] HTTPS certificate active
- [ ] Callback URLs working
- [ ] Error handling tested
- [ ] Customer notification ready
- [ ] Support team informed

---

**Ready for Live Payments!** 🚀

Once you complete these steps, your e-commerce site will accept real payments through SSL Commerz.
