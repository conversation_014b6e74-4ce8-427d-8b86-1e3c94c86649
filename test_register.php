<?php
// Debug Registration Test
require_once 'config/session.php';
require_once 'config/database.php';
require_once 'config/functions.php';

echo "<h2>Registration Debug Test</h2>";

if ($_POST) {
    echo "<h3>POST Data Received:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    echo "<h3>FILES Data:</h3>";
    echo "<pre>";
    print_r($_FILES);
    echo "</pre>";
    
    // Test basic validation
    $name = sanitize_input($_POST['name'] ?? '');
    $phone = sanitize_phone($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    echo "<h3>Processed Data:</h3>";
    echo "Name: " . htmlspecialchars($name) . "<br>";
    echo "Phone: " . htmlspecialchars($phone) . "<br>";
    echo "Password Length: " . strlen($password) . "<br>";
    echo "Passwords Match: " . ($password === $confirm_password ? 'Yes' : 'No') . "<br>";
    
    // Test phone validation
    echo "<h3>Phone Validation:</h3>";
    echo "Phone Length: " . strlen($phone) . "<br>";
    echo "Is Valid BD Mobile: " . (validate_bd_mobile($phone) ? 'Yes' : 'No') . "<br>";
    
    // Test database connection
    echo "<h3>Database Test:</h3>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM customers");
        $count = $stmt->fetchColumn();
        echo "Database connected successfully. Current customers: $count<br>";
        
        // Test if phone exists
        $stmt = $pdo->prepare("SELECT id FROM customers WHERE phone = ?");
        $stmt->execute([$phone]);
        $existing = $stmt->fetch();
        echo "Phone exists in database: " . ($existing ? 'Yes (ID: ' . $existing['id'] . ')' : 'No') . "<br>";
        
    } catch(PDOException $e) {
        echo "Database error: " . $e->getMessage() . "<br>";
    }
    
    // Test image upload
    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] !== UPLOAD_ERR_NO_FILE) {
        echo "<h3>Image Upload Test:</h3>";
        echo "Upload Error Code: " . $_FILES['profile_image']['error'] . "<br>";
        echo "File Size: " . $_FILES['profile_image']['size'] . " bytes<br>";
        echo "File Type: " . $_FILES['profile_image']['type'] . "<br>";
        
        if ($_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $upload_result = upload_image($_FILES['profile_image'], 'uploads/customers/');
            echo "Upload Result: " . ($upload_result['success'] ? 'Success' : 'Failed') . "<br>";
            if (!$upload_result['success']) {
                echo "Upload Error: " . $upload_result['message'] . "<br>";
            } else {
                echo "Uploaded File: " . $upload_result['filename'] . "<br>";
            }
        }
    }
    
    // Attempt actual registration
    echo "<h3>Registration Attempt:</h3>";
    
    if (empty($name) || empty($phone) || empty($password)) {
        echo "❌ Validation failed: Missing required fields<br>";
    } elseif ($password !== $confirm_password) {
        echo "❌ Validation failed: Passwords don't match<br>";
    } elseif (strlen($password) < 6) {
        echo "❌ Validation failed: Password too short<br>";
    } elseif (strlen($phone) < 11) {
        echo "❌ Validation failed: Phone too short<br>";
    } elseif (!validate_bd_mobile($phone)) {
        echo "❌ Validation failed: Invalid phone format<br>";
    } else {
        try {
            // Check if phone already exists
            $stmt = $pdo->prepare("SELECT id FROM customers WHERE phone = ?");
            $stmt->execute([$phone]);
            if ($stmt->fetch()) {
                echo "❌ Registration failed: Phone already exists<br>";
            } else {
                // Try to insert
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT INTO customers (name, password, phone) VALUES (?, ?, ?)");
                $result = $stmt->execute([$name, $hashed_password, $phone]);
                
                if ($result) {
                    echo "✅ Registration successful!<br>";
                    echo "Customer ID: " . $pdo->lastInsertId() . "<br>";
                } else {
                    echo "❌ Registration failed: Database insert failed<br>";
                }
            }
        } catch(PDOException $e) {
            echo "❌ Database error: " . $e->getMessage() . "<br>";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Debug Test</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Hind Siliguri', Arial, sans-serif; max-width: 800px; margin: 2rem auto; padding: 2rem; }
        h2, h3 { color: #333; }
        pre { background: #f8f9fa; padding: 1rem; border-radius: 5px; overflow-x: auto; }
        .form-container { background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-top: 2rem; }
        .form-group { margin-bottom: 1rem; }
        label { display: block; margin-bottom: 0.5rem; font-weight: 600; }
        input, textarea { width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 5px; font-size: 1rem; }
        button { background: #667eea; color: white; padding: 0.75rem 2rem; border: none; border-radius: 5px; cursor: pointer; font-size: 1rem; }
        button:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <div class="form-container">
        <h3>Test Registration Form</h3>
        <form method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label for="name">নাম *</label>
                <input type="text" name="name" id="name" value="টেস্ট ইউজার" required>
            </div>
            
            <div class="form-group">
                <label for="phone">মোবাইল নম্বর *</label>
                <input type="tel" name="phone" id="phone" value="01712345678" required>
            </div>
            
            <div class="form-group">
                <label for="email">ইমেইল (ঐচ্ছিক)</label>
                <input type="email" name="email" id="email" value="">
            </div>
            
            <div class="form-group">
                <label for="password">পাসওয়ার্ড *</label>
                <input type="password" name="password" id="password" value="123456" required>
            </div>
            
            <div class="form-group">
                <label for="confirm_password">পাসওয়ার্ড নিশ্চিত করুন *</label>
                <input type="password" name="confirm_password" id="confirm_password" value="123456" required>
            </div>
            
            <div class="form-group">
                <label for="profile_image">প্রোফাইল ছবি (ঐচ্ছিক)</label>
                <input type="file" name="profile_image" id="profile_image" accept="image/*">
            </div>
            
            <div class="form-group">
                <label for="address">ঠিকানা</label>
                <textarea name="address" id="address" rows="3"></textarea>
            </div>
            
            <div class="form-group">
                <label for="city">শহর</label>
                <input type="text" name="city" id="city" value="">
            </div>
            
            <div class="form-group">
                <label for="postal_code">পোস্টাল কোড</label>
                <input type="text" name="postal_code" id="postal_code" value="">
            </div>
            
            <div class="form-group">
                <button type="submit">টেস্ট রেজিস্ট্রেশন</button>
            </div>
        </form>
    </div>
    
    <p><a href="register.php">← মূল রেজিস্ট্রেশন পেজে ফিরে যান</a></p>
</body>
</html>
