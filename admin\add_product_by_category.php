<?php
require_once '../includes/error_handler.php';
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../includes/image_handler.php';

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Get categories
$stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
$categories = $stmt->fetchAll();

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_product') {
    try {
        $name = trim(safe_post('name'));
        $description = trim(safe_post('description'));
        $price = floatval(safe_post('price', 0));
        $stock_quantity = intval(safe_post('stock_quantity', 0));
        $category_id = intval(safe_post('category_id', 0));
        $sku = trim(safe_post('sku'));
        
        // Validation
        if (empty($name) || empty($price) || empty($category_id)) {
            throw new Exception('নাম, দাম এবং ক্যাটাগরি আবশ্যক।');
        }
        
        if ($price <= 0) {
            throw new Exception('দাম অবশ্যই ০ এর চেয়ে বেশি হতে হবে।');
        }
        
        if ($stock_quantity < 0) {
            throw new Exception('স্টক পরিমাণ ০ বা তার বেশি হতে হবে।');
        }
        
        // Check if SKU already exists
        if (!empty($sku)) {
            $stmt = $pdo->prepare("SELECT id FROM products WHERE sku = ? AND id != ?");
            $stmt->execute([$sku, 0]);
            if ($stmt->fetch()) {
                throw new Exception('এই SKU ইতিমধ্যে ব্যবহৃত হয়েছে।');
            }
        }
        
        // Handle image upload
        $imageName = null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $errors = ImageHandler::validateImage($_FILES['image']);
            if (!empty($errors)) {
                throw new Exception('ছবি আপলোড এরর: ' . implode(', ', $errors));
            }
            
            $imageName = ImageHandler::generateUniqueFilename($_FILES['image']['name'], 'product_');
            $destination = '../uploads/products/' . $imageName;
            
            if (!ImageHandler::moveUploadedFile($_FILES['image'], $destination)) {
                throw new Exception('ছবি সংরক্ষণে সমস্যা হয়েছে।');
            }
            
            // Resize image if needed
            $extension = strtolower(pathinfo($imageName, PATHINFO_EXTENSION));
            if ($extension !== 'svg') {
                $resizedPath = '../uploads/products/resized_' . $imageName;
                if (ImageHandler::resizeImage($destination, $resizedPath, 800, 600)) {
                    unlink($destination);
                    $imageName = 'resized_' . $imageName;
                }
            }
        }
        
        // Insert product
        $stmt = $pdo->prepare("INSERT INTO products (name, description, price, stock_quantity, category_id, sku, image, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW())");
        $stmt->execute([$name, $description, $price, $stock_quantity, $category_id, $sku, $imageName]);
        
        $message = 'পণ্য সফলভাবে যোগ করা হয়েছে!';
        $messageType = 'success';
        
        // Clear form data
        $_POST = [];
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ক্যাটাগরি অনুসারে পণ্য যোগ করুন</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            padding: 30px;
        }
        
        .category-sidebar {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            height: fit-content;
        }
        
        .category-list {
            list-style: none;
        }
        
        .category-item {
            margin-bottom: 10px;
        }
        
        .category-btn {
            width: 100%;
            padding: 15px 20px;
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .category-btn:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }
        
        .category-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .product-form {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
        }
        
        .form-section {
            margin-bottom: 25px;
        }
        
        .form-section h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .required {
            color: #dc3545;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-control.error {
            border-color: #dc3545;
        }
        
        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }
        
        .file-upload-area {
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .file-upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        
        .file-upload-area.dragover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        
        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .upload-text {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            font-size: 14px;
            color: #999;
        }
        
        .image-preview {
            margin-top: 15px;
            text-align: center;
        }
        
        .preview-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
        
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e0e0e0;
        }
        
        .message {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .supported-formats {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }
        
        .format-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .format-tag {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-plus-circle"></i> ক্যাটাগরি অনুসারে পণ্য যোগ করুন</h1>
            <p>আপনার দোকানে নতুন পণ্য যোগ করুন এবং ক্যাটাগরি অনুসারে সাজান</p>
        </div>

        <div class="main-content">
            <!-- Category Sidebar -->
            <div class="category-sidebar">
                <h3><i class="fas fa-tags"></i> ক্যাটাগরি নির্বাচন করুন</h3>
                <ul class="category-list">
                    <?php foreach ($categories as $category): ?>
                        <li class="category-item">
                            <button type="button" class="category-btn" onclick="selectCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['name']); ?>')">
                                <i class="fas fa-folder"></i>
                                <span><?php echo htmlspecialchars($category['name']); ?></span>
                            </button>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <!-- Product Form -->
            <div class="product-form">
                <?php if ($message): ?>
                    <div class="message <?php echo $messageType; ?>">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data" id="productForm">
                    <input type="hidden" name="action" value="add_product">
                    <input type="hidden" name="category_id" id="selectedCategoryId" value="">

                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3><i class="fas fa-info-circle"></i> মৌলিক তথ্য</h3>
                        
                        <div class="form-group">
                            <label for="selected-category">নির্বাচিত ক্যাটাগরি <span class="required">*</span></label>
                            <input type="text" id="selected-category" class="form-control" readonly placeholder="একটি ক্যাটাগরি নির্বাচন করুন">
                        </div>

                        <div class="form-group">
                            <label for="name">পণ্যের নাম <span class="required">*</span></label>
                            <input type="text" id="name" name="name" class="form-control" required placeholder="পণ্যের নাম লিখুন" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="sku">SKU (স্টক কিপিং ইউনিট)</label>
                            <input type="text" id="sku" name="sku" class="form-control" placeholder="যেমন: PROD-001" value="<?php echo htmlspecialchars($_POST['sku'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="description">বিবরণ</label>
                            <textarea id="description" name="description" class="form-control" placeholder="পণ্যের বিস্তারিত বিবরণ লিখুন"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        </div>
                    </div>

                    <!-- Pricing & Stock -->
                    <div class="form-section">
                        <h3><i class="fas fa-dollar-sign"></i> দাম ও স্টক</h3>
                        
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div class="form-group">
                                <label for="price">দাম (৳) <span class="required">*</span></label>
                                <input type="number" id="price" name="price" class="form-control" step="0.01" min="0" required placeholder="0.00" value="<?php echo htmlspecialchars($_POST['price'] ?? ''); ?>">
                            </div>

                            <div class="form-group">
                                <label for="stock_quantity">স্টক পরিমাণ</label>
                                <input type="number" id="stock_quantity" name="stock_quantity" class="form-control" min="0" placeholder="0" value="<?php echo htmlspecialchars($_POST['stock_quantity'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Image Upload -->
                    <div class="form-section">
                        <h3><i class="fas fa-image"></i> পণ্যের ছবি</h3>
                        
                        <div class="file-upload-area" onclick="document.getElementById('image').click()">
                            <input type="file" id="image" name="image" class="file-input" accept="image/*,.svg" onchange="previewImage(this)">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-text">ছবি আপলোড করতে ক্লিক করুন বা ড্র্যাগ করুন</div>
                            <div class="upload-hint">সর্বোচ্চ ফাইল সাইজ: 10MB</div>
                        </div>

                        <div class="image-preview" id="imagePreview" style="display: none;">
                            <img id="previewImg" class="preview-image" alt="Preview">
                        </div>

                        <div class="supported-formats">
                            <strong>সাপোর্টেড ফরম্যাট:</strong>
                            <div class="format-tags">
                                <span class="format-tag">JPG</span>
                                <span class="format-tag">PNG</span>
                                <span class="format-tag">GIF</span>
                                <span class="format-tag">WebP</span>
                                <span class="format-tag">BMP</span>
                                <span class="format-tag">SVG</span>
                                <span class="format-tag">TIFF</span>
                                <span class="format-tag">ICO</span>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fas fa-undo"></i> রিসেট
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-plus"></i> পণ্য যোগ করুন
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Category selection
        function selectCategory(categoryId, categoryName) {
            // Update hidden input
            document.getElementById('selectedCategoryId').value = categoryId;
            document.getElementById('selected-category').value = categoryName;
            
            // Update active state
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.closest('.category-btn').classList.add('active');
            
            // Enable form
            document.getElementById('submitBtn').disabled = false;
        }

        // Image preview
        function previewImage(input) {
            const file = input.files[0];
            const preview = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
            }
        }

        // Drag and drop
        const uploadArea = document.querySelector('.file-upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('image').files = files;
                previewImage(document.getElementById('image'));
            }
        });

        // Form validation
        document.getElementById('productForm').addEventListener('submit', function(e) {
            const categoryId = document.getElementById('selectedCategoryId').value;
            const name = document.getElementById('name').value.trim();
            const price = document.getElementById('price').value;
            
            if (!categoryId) {
                e.preventDefault();
                alert('অনুগ্রহ করে একটি ক্যাটাগরি নির্বাচন করুন।');
                return;
            }
            
            if (!name) {
                e.preventDefault();
                alert('পণ্যের নাম আবশ্যক।');
                document.getElementById('name').focus();
                return;
            }
            
            if (!price || parseFloat(price) <= 0) {
                e.preventDefault();
                alert('সঠিক দাম দিন।');
                document.getElementById('price').focus();
                return;
            }
        });

        // Reset form
        function resetForm() {
            document.getElementById('productForm').reset();
            document.getElementById('selectedCategoryId').value = '';
            document.getElementById('selected-category').value = '';
            document.getElementById('imagePreview').style.display = 'none';
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById('submitBtn').disabled = true;
        }

        // Initialize
        document.getElementById('submitBtn').disabled = true;
    </script>
</body>
</html>
