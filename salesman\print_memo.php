<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if salesman is logged in
if (!isset($_SESSION['salesman_id'])) {
    redirect('login.php');
}

$salesman_id = $_SESSION['salesman_id'];
$sale_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$sale_id) {
    redirect('sales.php');
}

try {
    // Get sale details
    $stmt = $pdo->prepare("SELECT ps.*, s.name as salesman_name, s.employee_id 
                          FROM pos_sales ps 
                          LEFT JOIN salesman s ON ps.salesman_id = s.id 
                          WHERE ps.id = ? AND ps.salesman_id = ?");
    $stmt->execute([$sale_id, $salesman_id]);
    $sale = $stmt->fetch();
    
    if (!$sale) {
        redirect('sales.php');
    }
    
    // Get sale items
    $stmt = $pdo->prepare("SELECT psi.*, p.name as product_name
                          FROM pos_sale_items psi 
                          LEFT JOIN products p ON psi.product_id = p.id
                          WHERE psi.sale_id = ?");
    $stmt->execute([$sale_id]);
    $sale_items = $stmt->fetchAll();
    
    // Payment methods
    $payment_methods = [
        'cash' => 'নগদ',
        'card' => 'কার্ড',
        'mobile_banking' => 'মোবাইল ব্যাংকিং'
    ];
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    redirect('sales.php');
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিক্রয় মেমো - <?php echo $sale['sale_number']; ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background: white;
            color: #333;
            line-height: 1.4;
        }
        
        .memo-container {
            max-width: 400px;
            margin: 20px auto;
            padding: 20px;
            border: 2px solid #333;
            background: white;
        }
        
        .memo-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        
        .shop-name {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .memo-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .sale-info {
            margin-bottom: 15px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .items-section {
            margin-bottom: 15px;
        }
        
        .items-header {
            border-bottom: 1px solid #333;
            padding-bottom: 5px;
            margin-bottom: 10px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .item-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.85rem;
        }
        
        .item-name {
            flex: 1;
            margin-right: 10px;
        }
        
        .item-qty {
            width: 30px;
            text-align: center;
        }
        
        .item-price {
            width: 60px;
            text-align: right;
        }
        
        .total-section {
            border-top: 2px solid #333;
            padding-top: 10px;
            margin-top: 15px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .final-total {
            font-weight: 700;
            font-size: 1rem;
            border-top: 1px solid #333;
            padding-top: 5px;
            margin-top: 5px;
        }
        
        .memo-footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #333;
            font-size: 0.8rem;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            .memo-container {
                margin: 0;
                border: none;
                max-width: none;
                width: 100%;
            }
            .no-print {
                display: none;
            }
        }
        
        .print-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Hind Siliguri', Arial, sans-serif;
            font-size: 0.9rem;
            margin: 10px 5px;
        }
        
        .print-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="memo-container">
        <div class="memo-header">
            <div class="shop-name">আমার দোকান</div>
            <div class="memo-title">বিক্রয় মেমো</div>
        </div>
        
        <div class="sale-info">
            <div class="info-row">
                <span>মেমো নং:</span>
                <span><?php echo $sale['sale_number']; ?></span>
            </div>
            <div class="info-row">
                <span>তারিখ:</span>
                <span><?php echo date('d/m/Y H:i', strtotime($sale['created_at'])); ?></span>
            </div>
            <div class="info-row">
                <span>কাস্টমার:</span>
                <span><?php echo $sale['customer_name'] ?: 'ওয়াক-ইন কাস্টমার'; ?></span>
            </div>
            <div class="info-row">
                <span>সেলসম্যান:</span>
                <span><?php echo $sale['salesman_name']; ?></span>
            </div>
        </div>
        
        <div class="items-section">
            <div class="items-header">
                <div style="display: flex; justify-content: space-between;">
                    <span>পণ্য</span>
                    <span style="width: 30px; text-align: center;">পরিমাণ</span>
                    <span style="width: 60px; text-align: right;">দাম</span>
                </div>
            </div>
            
            <?php foreach ($sale_items as $item): ?>
            <div class="item-row">
                <span class="item-name"><?php echo htmlspecialchars($item['product_name']); ?></span>
                <span class="item-qty"><?php echo $item['quantity']; ?></span>
                <span class="item-price">৳<?php echo number_format($item['total_price'], 2); ?></span>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="total-section">
            <div class="total-row">
                <span>মোট:</span>
                <span>৳<?php echo number_format($sale['total_amount'], 2); ?></span>
            </div>
            
            <?php if ($sale['discount_amount'] > 0): ?>
            <div class="total-row">
                <span>ছাড়:</span>
                <span>-৳<?php echo number_format($sale['discount_amount'], 2); ?></span>
            </div>
            <?php endif; ?>
            
            <div class="total-row final-total">
                <span>চূড়ান্ত মোট:</span>
                <span>৳<?php echo number_format($sale['final_amount'], 2); ?></span>
            </div>
            
            <div class="total-row">
                <span>পেমেন্ট:</span>
                <span><?php echo $payment_methods[$sale['payment_method']]; ?></span>
            </div>
        </div>
        
        <div class="memo-footer">
            <p>ধন্যবাদ! আবার আসবেন।</p>
            <p>যোগাযোগ: ০১৭xxxxxxxx</p>
        </div>
    </div>
    
    <div class="no-print" style="text-align: center; margin: 20px;">
        <button class="print-btn" onclick="window.print()">প্রিন্ট করুন</button>
        <button class="print-btn" onclick="window.close()" style="background: #6c757d;">বন্ধ করুন</button>
    </div>
    
    <script>
        // Auto print when page loads
        window.onload = function() {
            // Uncomment the line below if you want auto-print
            // window.print();
        }
    </script>
</body>
</html>
