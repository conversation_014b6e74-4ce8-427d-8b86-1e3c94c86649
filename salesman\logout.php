<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Log the logout activity if salesman is logged in
if (isset($_SESSION['salesman_id'])) {
    log_activity('salesman_logout', "Salesman logged out: {$_SESSION['salesman_name']}", $_SESSION['salesman_id'], 'salesman');
}

// Clear salesman session
unset($_SESSION['salesman_id']);
unset($_SESSION['salesman_name']);
unset($_SESSION['salesman_email']);
unset($_SESSION['employee_id']);

// Redirect to login page
redirect('login.php');
?>
