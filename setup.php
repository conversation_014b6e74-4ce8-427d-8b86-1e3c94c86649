<?php
// Database Setup Script
// This script will create the database and tables automatically

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'dokan_db';

echo "<h2>অনলাইন দোকান ডাটাবেস সেটআপ</h2>";

try {
    // First, connect without specifying database to create it
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database
    echo "<p>ডাটাবেস তৈরি করা হচ্ছে...</p>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✓ ডাটাবেস '$database' সফলভাবে তৈরি হয়েছে।</p>";
    
    // Now connect to the specific database
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>টেবিল তৈরি করা হচ্ছে...</p>";
    
    // Categories table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            image VARCHAR(255),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✓ Categories টেবিল তৈরি হয়েছে।</p>";
    
    // Products table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            price DECIMAL(10,2) NOT NULL,
            discount_price DECIMAL(10,2) DEFAULT NULL,
            category_id INT,
            image VARCHAR(255),
            stock_quantity INT DEFAULT 0,
            status ENUM('active', 'inactive') DEFAULT 'active',
            featured BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
        )
    ");
    echo "<p style='color: green;'>✓ Products টেবিল তৈরি হয়েছে।</p>";
    
    // Customers table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS customers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) UNIQUE NULL,
            password VARCHAR(255) NOT NULL,
            phone VARCHAR(20) UNIQUE NOT NULL,
            profile_image VARCHAR(255) NULL,
            address TEXT,
            city VARCHAR(50),
            postal_code VARCHAR(10),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✓ Customers টেবিল তৈরি হয়েছে।</p>";
    
    // Admin users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            role ENUM('admin', 'manager') DEFAULT 'admin',
            status ENUM('active', 'inactive') DEFAULT 'active',
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✓ Admin Users টেবিল তৈরি হয়েছে।</p>";
    
    // Orders table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            customer_id INT NOT NULL,
            order_number VARCHAR(20) UNIQUE NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
            payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
            payment_method ENUM('cash_on_delivery', 'bkash', 'nagad', 'bank_transfer') DEFAULT 'cash_on_delivery',
            shipping_address TEXT NOT NULL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
        )
    ");
    echo "<p style='color: green;'>✓ Orders টেবিল তৈরি হয়েছে।</p>";
    
    // Order items table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS order_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id INT NOT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            total DECIMAL(10,2) NOT NULL,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
        )
    ");
    echo "<p style='color: green;'>✓ Order Items টেবিল তৈরি হয়েছে।</p>";
    
    // Cart table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS cart (
            id INT AUTO_INCREMENT PRIMARY KEY,
            customer_id INT NOT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            UNIQUE KEY unique_cart_item (customer_id, product_id)
        )
    ");
    echo "<p style='color: green;'>✓ Cart টেবিল তৈরি হয়েছে।</p>";
    
    // Activity logs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            user_type ENUM('admin', 'customer') DEFAULT 'admin',
            action VARCHAR(100) NOT NULL,
            details TEXT,
            ip_address VARCHAR(45),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✓ Activity Logs টেবিল তৈরি হয়েছে।</p>";

    // Password reset tokens table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS password_reset_tokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            customer_id INT NOT NULL,
            token VARCHAR(255) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            used BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
            INDEX idx_token (token),
            INDEX idx_expires (expires_at)
        )
    ");
    echo "<p style='color: green;'>✓ Password Reset Tokens টেবিল তৈরি হয়েছে।</p>";
    
    echo "<p>ডিফল্ট ডেটা যোগ করা হচ্ছে...</p>";
    
    // Check if admin user already exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin_users WHERE username = 'admin'");
    $stmt->execute();
    $adminExists = $stmt->fetchColumn();
    
    if (!$adminExists) {
        // Insert default admin user (password: admin123)
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->exec("
            INSERT INTO admin_users (username, email, password, full_name, role) VALUES 
            ('admin', '<EMAIL>', '$hashedPassword', 'System Administrator', 'admin')
        ");
        echo "<p style='color: green;'>✓ ডিফল্ট অ্যাডমিন ইউজার তৈরি হয়েছে (ইউজারনেম: admin, পাসওয়ার্ড: admin123)।</p>";
    }
    
    // Check if categories already exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories");
    $stmt->execute();
    $categoryCount = $stmt->fetchColumn();
    
    if ($categoryCount == 0) {
        // Insert sample categories
        $pdo->exec("
            INSERT INTO categories (name, description) VALUES 
            ('ইলেকট্রনিক্স', 'মোবাইল, ল্যাপটপ, টিভি এবং অন্যান্য ইলেকট্রনিক পণ্য'),
            ('ফ্যাশন', 'পুরুষ ও মহিলাদের পোশাক, জুতা এবং এক্সেসরিজ'),
            ('বই', 'শিক্ষামূলক বই, উপন্যাস, গল্পের বই'),
            ('খাবার', 'স্থানীয় এবং আমদানিকৃত খাবার'),
            ('স্বাস্থ্য ও সৌন্দর্য', 'স্বাস্থ্য পণ্য, প্রসাধনী এবং ব্যক্তিগত যত্ন')
        ");
        echo "<p style='color: green;'>✓ নমুনা ক্যাটেগরি যোগ করা হয়েছে।</p>";
    }
    
    // Check if products already exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM products");
    $stmt->execute();
    $productCount = $stmt->fetchColumn();
    
    if ($productCount == 0) {
        // Insert sample products
        $pdo->exec("
            INSERT INTO products (name, description, price, category_id, stock_quantity, featured) VALUES 
            ('Samsung Galaxy A54', 'সর্বশেষ Samsung Galaxy A54 স্মার্টফোন', 35000.00, 1, 50, TRUE),
            ('Dell Inspiron Laptop', 'Dell Inspiron 15 3000 সিরিজ ল্যাপটপ', 55000.00, 1, 25, TRUE),
            ('পুরুষদের শার্ট', 'উচ্চ মানের কটন শার্ট', 1200.00, 2, 100, FALSE),
            ('বাংলা উপন্যাস', 'জনপ্রিয় বাংলা উপন্যাস সংগ্রহ', 300.00, 3, 200, FALSE),
            ('বাদাম মিশ্রণ', 'স্বাস্থ্যকর বাদাম মিশ্রণ', 450.00, 4, 80, TRUE)
        ");
        echo "<p style='color: green;'>✓ নমুনা পণ্য যোগ করা হয়েছে।</p>";
    }
    
    echo "<h3 style='color: green;'>🎉 সেটআপ সম্পন্ন!</h3>";
    echo "<p><strong>এখন আপনি ওয়েবসাইট ব্যবহার করতে পারেন:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.php' target='_blank'>মূল ওয়েবসাইট দেখুন</a></li>";
    echo "<li><a href='admin/login.php' target='_blank'>অ্যাডমিন প্যানেল</a> (ইউজারনেম: admin, পাসওয়ার্ড: admin123)</li>";
    echo "</ul>";
    echo "<p style='color: orange;'><strong>নোট:</strong> সেটআপ সম্পন্ন হওয়ার পর এই ফাইলটি (setup.php) নিরাপত্তার জন্য মুছে ফেলুন।</p>";
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ এরর: " . $e->getMessage() . "</p>";
    echo "<p>দয়া করে নিশ্চিত করুন যে:</p>";
    echo "<ul>";
    echo "<li>XAMPP চালু আছে</li>";
    echo "<li>MySQL সার্ভিস চালু আছে</li>";
    echo "<li>ডাটাবেস কনফিগারেশন সঠিক আছে</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডাটাবেস সেটআপ</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8f9fa;
        }
        h2, h3 {
            color: #333;
        }
        p {
            line-height: 1.6;
        }
        ul {
            margin: 1rem 0;
        }
        a {
            color: #667eea;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
</body>
</html>
