<?php
require_once 'config/session.php';
require_once 'config/database.php';
require_once 'config/functions.php';

// Redirect if already logged in
if (isset($_SESSION['customer_id'])) {
    redirect('index.php');
}

$message = '';

if ($_POST) {
    $message = '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-check-circle"></i> Form submitted successfully!</div>';

    // Continue with the working logic...
    $name = sanitize_input($_POST['name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $phone = sanitize_phone($_POST['phone'] ?? '');
    $address = sanitize_input($_POST['address'] ?? '');
    $city = sanitize_input($_POST['city'] ?? '');
    $postal_code = sanitize_input($_POST['postal_code'] ?? '');

    // Handle profile image upload
    $profile_image = null;
    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
        $upload_result = upload_image($_FILES['profile_image'], 'uploads/customers/');
        if ($upload_result['success']) {
            $profile_image = $upload_result['filename'];
        } else {
            $error = $upload_result['message'];
        }
    }

    // Validation
    if (!isset($error)) {
        if (empty($name) || empty($phone) || empty($password)) {
            $message .= '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-exclamation-triangle"></i> নাম, মোবাইল নম্বর এবং পাসওয়ার্ড আবশ্যক।</div>';
        } elseif ($password !== $confirm_password) {
            $message .= '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-exclamation-triangle"></i> পাসওয়ার্ড মিলছে না।</div>';
        } elseif (strlen($password) < 6) {
            $message .= '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-exclamation-triangle"></i> পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে।</div>';
        } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $message .= '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-exclamation-triangle"></i> সঠিক ইমেইল ঠিকানা দিন।</div>';
        } elseif (strlen($phone) < 11) {
            $message .= '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-exclamation-triangle"></i> মোবাইল নম্বর কমপক্ষে ১১ সংখ্যার হতে হবে।</div>';
        } elseif (!validate_bd_mobile($phone)) {
            $message .= '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-exclamation-triangle"></i> সঠিক বাংলাদেশী মোবাইল নম্বর দিন (01XXXXXXXXX)।</div>';
        } else {
            try {
                // Check if phone already exists
                $stmt = $pdo->prepare("SELECT id FROM customers WHERE phone = ?");
                $stmt->execute([$phone]);
                if ($stmt->fetch()) {
                    $message .= '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-exclamation-triangle"></i> এই মোবাইল নম্বর দিয়ে ইতিমধ্যে একটি অ্যাকাউন্ট রয়েছে।</div>';
                } elseif (!empty($email)) {
                    // Check if email already exists (only if email is provided)
                    $stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ?");
                    $stmt->execute([$email]);
                    if ($stmt->fetch()) {
                        $message .= '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-exclamation-triangle"></i> এই ইমেইল ঠিকানা দিয়ে ইতিমধ্যে একটি অ্যাকাউন্ট রয়েছে।</div>';
                    }
                }

                if (strpos($message, 'background: #f8d7da') === false && strpos($message, 'background: #fff3cd') === false) {
                    // Create new customer
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $email_value = !empty($email) ? $email : null;
                    $stmt = $pdo->prepare("INSERT INTO customers (name, email, password, phone, profile_image, address, city, postal_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                    $result = $stmt->execute([$name, $email_value, $hashed_password, $phone, $profile_image, $address, $city, $postal_code]);

                    if ($result) {
                        $customer_id = $pdo->lastInsertId();
                        // Log the registration
                        log_activity('Customer Registration', "New customer registered: $name ($phone)", $customer_id, 'customer');
                        $message .= '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-check-circle"></i> অ্যাকাউন্ট সফলভাবে তৈরি হয়েছে! Customer ID: ' . $customer_id . '<br><a href="login.php">এখানে লগইন করুন</a></div>';
                    } else {
                        $message .= '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-exclamation-triangle"></i> অ্যাকাউন্ট তৈরি করতে সমস্যা হয়েছে।</div>';
                    }
                }
            } catch(PDOException $e) {
                $message .= '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-exclamation-triangle"></i> ডাটাবেস এরর: ' . $e->getMessage() . '</div>';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রেজিস্টার - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .register-container {
            max-width: 600px;
            margin: 2rem auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .register-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
        .alert {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
            border: 1px solid;
        }
        .alert-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <a href="index.php">
                    <i class="fas fa-store"></i>
                    <?php echo SITE_NAME; ?>
                </a>
            </div>
            <nav class="nav-menu">
                <a href="index.php"><i class="fas fa-home"></i> হোম</a>
                <a href="login.php"><i class="fas fa-sign-in-alt"></i> লগইন</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="register-container">
            <div class="register-header">
                <h2><i class="fas fa-user-plus"></i> নতুন অ্যাকাউন্ট তৈরি করুন</h2>
                <p>আমাদের সাথে যোগ দিন এবং সহজে কেনাকাটা করুন</p>
            </div>

            <?php if ($message): ?>
                <?php echo $message; ?>
            <?php endif; ?>

            <form method="POST" enctype="multipart/form-data" id="registerForm">
                <div class="form-group">
                    <label for="name">পূর্ণ নাম *</label>
                    <input type="text" name="name" id="name" class="form-control"
                           value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" required>
                </div>

                <div class="form-group">
                    <label for="profile_image">প্রোফাইল ছবি (ঐচ্ছিক)</label>
                    <input type="file" name="profile_image" id="profile_image" class="form-control" accept="image/*">
                    <small>JPG, PNG, GIF ফরম্যাট সাপোর্ট করে। সর্বোচ্চ ২MB।</small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">মোবাইল নম্বর *</label>
                        <input type="tel" name="phone" id="phone" class="form-control"
                               value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>"
                               placeholder="01XXXXXXXXX" required>
                    </div>

                    <div class="form-group">
                        <label for="email">ইমেইল ঠিকানা (ঐচ্ছিক)</label>
                        <input type="email" name="email" id="email" class="form-control"
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                               placeholder="<EMAIL>">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="password">পাসওয়ার্ড *</label>
                        <input type="password" name="password" id="password" class="form-control" required>
                        <small>কমপক্ষে ৬ অক্ষরের হতে হবে</small>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">পাসওয়ার্ড নিশ্চিত করুন *</label>
                        <input type="password" name="confirm_password" id="confirm_password" class="form-control" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address">ঠিকানা</label>
                    <textarea name="address" id="address" class="form-control" rows="2"><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="city">শহর</label>
                        <input type="text" name="city" id="city" class="form-control"
                               value="<?php echo isset($_POST['city']) ? htmlspecialchars($_POST['city']) : ''; ?>">
                    </div>

                    <div class="form-group">
                        <label for="postal_code">পোস্টাল কোড</label>
                        <input type="text" name="postal_code" id="postal_code" class="form-control"
                               value="<?php echo isset($_POST['postal_code']) ? htmlspecialchars($_POST['postal_code']) : ''; ?>">
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <i class="fas fa-user-plus"></i> অ্যাকাউন্ট তৈরি করুন
                    </button>
                </div>
            </form>

            <div class="text-center" style="margin-top: 2rem;">
                <p>ইতিমধ্যে অ্যাকাউন্ট আছে? <a href="login.php">এখানে লগইন করুন</a></p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 <?php echo SITE_NAME; ?>. সকল অধিকার সংরক্ষিত।</p>
        </div>
    </footer>

    <script>
        // Form validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (password !== confirmPassword) {
                e.preventDefault();
                alert('পাসওয়ার্ড মিলছে না।');
                return false;
            }

            if (password.length < 6) {
                e.preventDefault();
                alert('পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে।');
                return false;
            }
        });
    </script>
</body>
</html>
