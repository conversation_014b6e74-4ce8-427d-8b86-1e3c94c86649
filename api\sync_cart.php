<?php
require_once '../config/session.php';
require_once '../config/database.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $customer_id = $_SESSION['customer_id'];
    $cart_json = $_POST['cart'] ?? '';
    
    if (empty($cart_json)) {
        // Return empty cart
        $_SESSION['cart'] = [];
        echo json_encode(['success' => true, 'cart' => []]);
        exit;
    }
    
    $cart = json_decode($cart_json, true);
    
    if (!is_array($cart)) {
        echo json_encode(['success' => false, 'message' => 'Invalid cart data']);
        exit;
    }
    
    // Convert localStorage cart format to session cart format
    $_SESSION['cart'] = [];
    
    foreach ($cart as $item) {
        if (isset($item['id']) && isset($item['quantity']) && $item['quantity'] > 0) {
            $product_id = (int)$item['id'];
            $quantity = (int)$item['quantity'];
            $_SESSION['cart'][$product_id] = $quantity;
        }
    }
    
    echo json_encode(['success' => true, 'cart' => $_SESSION['cart']]);
    
} catch(Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
