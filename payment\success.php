<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';
require_once '../config/sslcommerz.php';

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    redirect('../login.php');
}

$message = '';
$order_info = null;

if ($_POST) {
    $tran_id = $_POST['tran_id'] ?? '';
    $val_id = $_POST['val_id'] ?? '';
    $amount = $_POST['amount'] ?? 0;
    $card_type = $_POST['card_type'] ?? '';
    $store_amount = $_POST['store_amount'] ?? 0;
    $bank_tran_id = $_POST['bank_tran_id'] ?? '';
    $status = $_POST['status'] ?? '';
    
    if ($status == 'VALID') {
        // Validate payment with SSL Commerz
        $validation = SSLCommerz::validate_payment($val_id, SSLC_STORE_ID, SSLC_STORE_PASSWORD);
        
        if ($validation && $validation['status'] == 'VALID') {
            try {
                // Find the order
                $stmt = $pdo->prepare("SELECT * FROM orders WHERE transaction_id = ? AND customer_id = ?");
                $stmt->execute([$tran_id, $_SESSION['customer_id']]);
                $order = $stmt->fetch();
                
                if ($order) {
                    // Update order payment status
                    $stmt = $pdo->prepare("UPDATE orders SET 
                        payment_status = 'paid', 
                        payment_method = 'sslcommerz',
                        payment_details = ?,
                        updated_at = NOW() 
                        WHERE id = ?");
                    
                    $payment_details = json_encode([
                        'val_id' => $val_id,
                        'bank_tran_id' => $bank_tran_id,
                        'card_type' => $card_type,
                        'store_amount' => $store_amount,
                        'payment_time' => date('Y-m-d H:i:s')
                    ]);
                    
                    $stmt->execute([$payment_details, $order['id']]);
                    
                    // Log activity
                    log_activity('Payment Success', "Payment completed for order #{$order['id']} - Amount: {$amount} BDT", $_SESSION['customer_id'], 'customer');
                    
                    $order_info = $order;
                    $message = '<div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> 
                        পেমেন্ট সফল হয়েছে! আপনার অর্ডার নিশ্চিত করা হয়েছে।
                    </div>';
                } else {
                    $message = '<div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i> 
                        অর্ডার খুঁজে পাওয়া যায়নি।
                    </div>';
                }
            } catch(PDOException $e) {
                $message = '<div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i> 
                    ডাটাবেস এরর: ' . $e->getMessage() . '
                </div>';
            }
        } else {
            $message = '<div class="alert alert-error">
                <i class="fas fa-times-circle"></i> 
                পেমেন্ট যাচাইকরণ ব্যর্থ হয়েছে।
            </div>';
        }
    } else {
        $message = '<div class="alert alert-error">
            <i class="fas fa-times-circle"></i> 
            পেমেন্ট সফল হয়নি। স্ট্যাটাস: ' . $status . '
        </div>';
    }
} else {
    $message = '<div class="alert alert-error">
        <i class="fas fa-exclamation-triangle"></i> 
        অবৈধ অনুরোধ।
    </div>';
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট সফল - <?php echo SITE_NAME; ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .payment-result {
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1rem;
        }
        .order-details {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1.5rem 0;
            text-align: left;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            color: #2c5aa0;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <a href="../index.php">
                    <i class="fas fa-store"></i>
                    <?php echo SITE_NAME; ?>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="payment-result">
            <?php if ($order_info): ?>
                <i class="fas fa-check-circle success-icon"></i>
                <h2>পেমেন্ট সফল!</h2>
                <p>আপনার অর্ডার সফলভাবে সম্পন্ন হয়েছে।</p>
                
                <div class="order-details">
                    <h4><i class="fas fa-receipt"></i> অর্ডার বিবরণ</h4>
                    <div class="detail-row">
                        <span>অর্ডার নম্বর:</span>
                        <span>#<?php echo $order_info['order_number']; ?></span>
                    </div>
                    <div class="detail-row">
                        <span>লেনদেন আইডি:</span>
                        <span><?php echo $tran_id; ?></span>
                    </div>
                    <div class="detail-row">
                        <span>পেমেন্ট পদ্ধতি:</span>
                        <span><?php echo htmlspecialchars($card_type); ?></span>
                    </div>
                    <div class="detail-row">
                        <span>মোট পরিমাণ:</span>
                        <span><?php echo format_price($amount); ?></span>
                    </div>
                </div>
                
                <div style="margin-top: 2rem;">
                    <a href="../orders.php" class="btn btn-primary">
                        <i class="fas fa-list"></i> আমার অর্ডার দেখুন
                    </a>
                    <a href="../index.php" class="btn btn-secondary" style="margin-left: 1rem;">
                        <i class="fas fa-home"></i> হোম পেজ
                    </a>
                </div>
            <?php else: ?>
                <i class="fas fa-times-circle" style="font-size: 4rem; color: #dc3545; margin-bottom: 1rem;"></i>
                <h2>পেমেন্ট সমস্যা</h2>
            <?php endif; ?>
            
            <?php echo $message; ?>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 <?php echo SITE_NAME; ?>. সকল অধিকার সংরক্ষিত।</p>
        </div>
    </footer>
</body>
</html>
