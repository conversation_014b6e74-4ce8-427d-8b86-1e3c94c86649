<?php
// Check Customers in Database
require_once 'config/database.php';

echo "<h2>Customers Database Check</h2>";

try {
    // Get all customers
    $stmt = $pdo->query("SELECT * FROM customers ORDER BY created_at DESC LIMIT 10");
    $customers = $stmt->fetchAll();
    
    echo "<h3>Recent Customers (Last 10):</h3>";
    
    if ($customers) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Name</th>";
        echo "<th style='padding: 8px;'>Phone</th>";
        echo "<th style='padding: 8px;'>Email</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Created</th>";
        echo "</tr>";
        
        foreach ($customers as $customer) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $customer['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['phone']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['email'] ?: 'N/A') . "</td>";
            echo "<td style='padding: 8px;'>" . $customer['status'] . "</td>";
            echo "<td style='padding: 8px;'>" . $customer['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No customers found in database.</p>";
    }
    
    // Count total customers
    $stmt = $pdo->query("SELECT COUNT(*) FROM customers");
    $total = $stmt->fetchColumn();
    echo "<p><strong>Total customers: $total</strong></p>";
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>Database Error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='register.php'>← Back to Registration</a></p>";
?>
