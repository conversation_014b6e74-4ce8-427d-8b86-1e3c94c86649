<?php
/**
 * Error Handler and Utility Functions
 * Handles common PHP errors and provides utility functions
 */

// Load function loader first to prevent conflicts
require_once __DIR__ . '/function_loader.php';

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to users
ini_set('log_errors', 1);

/**
 * Custom error handler
 */
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    // Don't handle errors that are suppressed with @
    if (!(error_reporting() & $errno)) {
        return false;
    }

    $errorTypes = [
        E_ERROR => 'Fatal Error',
        E_WARNING => 'Warning',
        E_PARSE => 'Parse Error',
        E_NOTICE => 'Notice',
        E_CORE_ERROR => 'Core Error',
        E_CORE_WARNING => 'Core Warning',
        E_COMPILE_ERROR => 'Compile Error',
        E_COMPILE_WARNING => 'Compile Warning',
        E_USER_ERROR => 'User Error',
        E_USER_WARNING => 'User Warning',
        E_USER_NOTICE => 'User Notice',
        // E_STRICT => 'Strict Notice', // Deprecated in PHP 8.0
        E_RECOVERABLE_ERROR => 'Recoverable Error',
        E_DEPRECATED => 'Deprecated',
        E_USER_DEPRECATED => 'User Deprecated'
    ];

    $errorType = isset($errorTypes[$errno]) ? $errorTypes[$errno] : 'Unknown Error';
    
    // Log error
    $logMessage = "[$errorType] $errstr in $errfile on line $errline";
    error_log($logMessage);

    // For development, you can uncomment this to see errors
    // echo "<div style='background: #ffebee; color: #c62828; padding: 10px; margin: 10px; border-radius: 5px;'>";
    // echo "<strong>$errorType:</strong> $errstr<br>";
    // echo "<strong>File:</strong> $errfile<br>";
    // echo "<strong>Line:</strong> $errline";
    // echo "</div>";

    return true;
}

// Set custom error handler
set_error_handler('customErrorHandler');

// All utility functions are now loaded from function_loader.php
// This file now only handles error management
?>
