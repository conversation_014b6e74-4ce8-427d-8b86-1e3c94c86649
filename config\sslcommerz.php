<?php
// SSL Commerz Configuration

// Load SSL settings from database
function load_ssl_settings() {
    global $pdo;

    $settings = [
        'ssl_store_id' => 'testbox',
        'ssl_store_password' => 'qwerty',
        'ssl_is_sandbox' => true,
        'ssl_is_enabled' => false
    ];

    try {
        if (isset($pdo)) {
            $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'ssl_%'");
            while ($row = $stmt->fetch()) {
                $key = str_replace('ssl_', '', $row['setting_key']);
                $value = $row['setting_value'];

                // Convert string values to appropriate types
                if ($key === 'is_sandbox' || $key === 'is_enabled') {
                    $settings['ssl_' . $key] = (bool)$value;
                } else {
                    $settings['ssl_' . $key] = $value;
                }
            }
        }
    } catch(PDOException $e) {
        // Use default settings if database error
    }

    return $settings;
}

// Load settings
$ssl_config = load_ssl_settings();

// Define constants
define('SSLC_STORE_ID', $ssl_config['ssl_store_id']);
define('SSLC_STORE_PASSWORD', $ssl_config['ssl_store_password']);
define('SSLC_IS_SANDBOX', $ssl_config['ssl_is_sandbox']);
define('SSLC_IS_ENABLED', $ssl_config['ssl_is_enabled']);

// SSL Commerz URLs
if (SSLC_IS_SANDBOX) {
    define('SSLC_SESSION_API', 'https://sandbox.sslcommerz.com/gwprocess/v4/api.php');
    define('SSLC_VALIDATION_API', 'https://sandbox.sslcommerz.com/validator/api/validationserverAPI.php');
} else {
    define('SSLC_SESSION_API', 'https://securepay.sslcommerz.com/gwprocess/v4/api.php');
    define('SSLC_VALIDATION_API', 'https://securepay.sslcommerz.com/validator/api/validationserverAPI.php');
}

// Site URLs
if (!defined('SITE_URL')) {
    define('SITE_URL', 'http://localhost/dokan');
}
define('SUCCESS_URL', SITE_URL . '/payment/success.php');
define('FAIL_URL', SITE_URL . '/payment/fail.php');
define('CANCEL_URL', SITE_URL . '/payment/cancel.php');
define('IPN_URL', SITE_URL . '/payment/ipn.php');

// SSL Commerz Helper Functions
class SSLCommerz {
    
    public static function initiate_payment($order_data) {
        $post_data = array();
        
        // Store Information
        $post_data['store_id'] = SSLC_STORE_ID;
        $post_data['store_passwd'] = SSLC_STORE_PASSWORD;
        
        // Customer Information
        $post_data['cus_name'] = $order_data['customer_name'];
        $post_data['cus_email'] = $order_data['customer_email'];
        $post_data['cus_add1'] = $order_data['customer_address'];
        $post_data['cus_city'] = $order_data['customer_city'] ?? 'Dhaka';
        $post_data['cus_state'] = $order_data['customer_state'] ?? 'Dhaka';
        $post_data['cus_postcode'] = $order_data['customer_postcode'] ?? '1000';
        $post_data['cus_country'] = 'Bangladesh';
        $post_data['cus_phone'] = $order_data['customer_phone'];
        
        // Transaction Information
        $post_data['total_amount'] = $order_data['total_amount'];
        $post_data['currency'] = 'BDT';
        $post_data['tran_id'] = $order_data['transaction_id'];
        $post_data['product_name'] = $order_data['product_name'];
        $post_data['product_category'] = 'General';
        $post_data['product_profile'] = 'general';
        
        // URLs
        $post_data['success_url'] = SUCCESS_URL;
        $post_data['fail_url'] = FAIL_URL;
        $post_data['cancel_url'] = CANCEL_URL;
        $post_data['ipn_url'] = IPN_URL;
        
        // Shipping Information
        $post_data['shipping_method'] = 'YES';
        $post_data['ship_name'] = $order_data['customer_name'];
        $post_data['ship_add1'] = $order_data['customer_address'];
        $post_data['ship_city'] = $order_data['customer_city'] ?? 'Dhaka';
        $post_data['ship_state'] = $order_data['customer_state'] ?? 'Dhaka';
        $post_data['ship_postcode'] = $order_data['customer_postcode'] ?? '1000';
        $post_data['ship_country'] = 'Bangladesh';
        
        // Make API Call
        $handle = curl_init();
        curl_setopt($handle, CURLOPT_URL, SSLC_SESSION_API);
        curl_setopt($handle, CURLOPT_TIMEOUT, 30);
        curl_setopt($handle, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($handle, CURLOPT_POST, 1);
        curl_setopt($handle, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($handle, CURLOPT_SSL_VERIFYPEER, FALSE);
        
        $content = curl_exec($handle);
        $code = curl_getinfo($handle, CURLINFO_HTTP_CODE);
        
        if($code == 200 && !curl_errno($handle)) {
            curl_close($handle);
            $sslcommerzResponse = $content;
        } else {
            curl_close($handle);
            return false;
        }
        
        $sslcz = json_decode($sslcommerzResponse, true);
        
        if(isset($sslcz['GatewayPageURL']) && $sslcz['GatewayPageURL'] != "") {
            return $sslcz['GatewayPageURL'];
        } else {
            return false;
        }
    }
    
    public static function validate_payment($val_id, $store_id, $store_passwd) {
        $validation_data = array(
            'val_id' => $val_id,
            'store_id' => $store_id,
            'store_passwd' => $store_passwd,
            'v' => 1,
            'format' => 'json'
        );
        
        $handle = curl_init();
        curl_setopt($handle, CURLOPT_URL, SSLC_VALIDATION_API);
        curl_setopt($handle, CURLOPT_TIMEOUT, 30);
        curl_setopt($handle, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($handle, CURLOPT_POST, 1);
        curl_setopt($handle, CURLOPT_POSTFIELDS, $validation_data);
        curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($handle, CURLOPT_SSL_VERIFYPEER, FALSE);
        
        $content = curl_exec($handle);
        $code = curl_getinfo($handle, CURLINFO_HTTP_CODE);
        
        if($code == 200 && !curl_errno($handle)) {
            curl_close($handle);
            $response = json_decode($content, true);
            return $response;
        } else {
            curl_close($handle);
            return false;
        }
    }
}
?>
