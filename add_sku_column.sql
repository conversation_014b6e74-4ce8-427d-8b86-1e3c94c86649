-- Add SKU column to products table
-- This script adds the missing 'sku' column to fix the "Unknown column 'sku'" error

USE dokan_db;

-- Add sku column to products table
ALTER TABLE products ADD COLUMN sku VARCHAR(100) DEFAULT NULL AFTER category_id;

-- Add unique index on sku column to prevent duplicates
ALTER TABLE products ADD UNIQUE KEY unique_sku (sku);

-- Show the updated table structure
DESCRIBE products;
