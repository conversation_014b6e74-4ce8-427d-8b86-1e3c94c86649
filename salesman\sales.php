<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if salesman is logged in
if (!isset($_SESSION['salesman_id'])) {
    redirect('login.php');
}

$salesman_id = $_SESSION['salesman_id'];

// Get filter parameters
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-01');
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
$payment_method = isset($_GET['payment_method']) ? $_GET['payment_method'] : '';
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query
$sql = "SELECT ps.*, 
        COUNT(psi.id) as item_count,
        GROUP_CONCAT(CONCAT(p.name, ' (', psi.quantity, ')') SEPARATOR ', ') as items_summary
        FROM pos_sales ps 
        LEFT JOIN pos_sale_items psi ON ps.id = psi.sale_id
        LEFT JOIN products p ON psi.product_id = p.id
        WHERE ps.salesman_id = ?";

$params = [$salesman_id];

// Add filters
if ($date_from) {
    $sql .= " AND DATE(ps.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $sql .= " AND DATE(ps.created_at) <= ?";
    $params[] = $date_to;
}

if ($payment_method) {
    $sql .= " AND ps.payment_method = ?";
    $params[] = $payment_method;
}

if ($search) {
    $sql .= " AND (ps.sale_number LIKE ? OR ps.customer_name LIKE ? OR ps.customer_phone LIKE ?)";
    $searchTerm = "%$search%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$sql .= " GROUP BY ps.id ORDER BY ps.created_at DESC LIMIT $limit OFFSET $offset";

try {
    // Get sales
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $sales = $stmt->fetchAll();
    
    // Get total count for pagination
    $countSql = str_replace("SELECT ps.*, COUNT(psi.id) as item_count, GROUP_CONCAT(CONCAT(p.name, ' (', psi.quantity, ')') SEPARATOR ', ') as items_summary", "SELECT COUNT(DISTINCT ps.id)", $sql);
    $countSql = str_replace("GROUP BY ps.id ORDER BY ps.created_at DESC LIMIT $limit OFFSET $offset", "", $countSql);
    
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $totalSales = $countStmt->fetchColumn();
    $totalPages = ceil($totalSales / $limit);
    
    // Get summary statistics
    $statsSql = "SELECT 
                COUNT(*) as total_count,
                SUM(final_amount) as total_amount,
                AVG(final_amount) as avg_amount
                FROM pos_sales ps WHERE ps.salesman_id = ?";
    $statsParams = [$salesman_id];
    
    if ($date_from) {
        $statsSql .= " AND DATE(ps.created_at) >= ?";
        $statsParams[] = $date_from;
    }
    
    if ($date_to) {
        $statsSql .= " AND DATE(ps.created_at) <= ?";
        $statsParams[] = $date_to;
    }
    
    if ($payment_method) {
        $statsSql .= " AND ps.payment_method = ?";
        $statsParams[] = $payment_method;
    }
    
    if ($search) {
        $statsSql .= " AND (ps.sale_number LIKE ? OR ps.customer_name LIKE ? OR ps.customer_phone LIKE ?)";
        $statsParams[] = $searchTerm;
        $statsParams[] = $searchTerm;
        $statsParams[] = $searchTerm;
    }
    
    $statsStmt = $pdo->prepare($statsSql);
    $statsStmt->execute($statsParams);
    $stats = $statsStmt->fetch();
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    $sales = [];
    $totalSales = 0;
    $totalPages = 0;
    $stats = ['total_count' => 0, 'total_amount' => 0, 'avg_amount' => 0];
}

$payment_methods = [
    'cash' => 'নগদ',
    'card' => 'কার্ড',
    'mobile_banking' => 'মোবাইল ব্যাংকিং'
];
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিক্রয় তালিকা - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo h1 {
            font-size: 1.5rem;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .nav-menu {
            background: white;
            padding: 1rem 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .nav-menu ul {
            list-style: none;
            display: flex;
            gap: 2rem;
        }
        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .nav-menu a:hover, .nav-menu a.active {
            background: #667eea;
            color: white;
        }
        .main-content {
            padding: 2rem 0;
        }
        .page-header {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .page-header h2 {
            margin-bottom: 1rem;
            color: #333;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .stat-card h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #333;
        }
        .stat-card p {
            color: #666;
            font-size: 0.9rem;
        }
        .filters {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: background-color 0.3s ease;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .sales-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .badge-success {
            background: #d4edda;
            color: #155724;
        }
        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        .pagination a,
        .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        .pagination a:hover {
            background: #667eea;
            color: white;
        }
        .pagination .current {
            background: #667eea;
            color: white;
        }
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            .nav-menu ul {
                flex-wrap: wrap;
                gap: 1rem;
            }
            .filters-grid {
                grid-template-columns: 1fr;
            }
            .table {
                font-size: 0.9rem;
            }
            .table th,
            .table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-cash-register"></i> POS সিস্টেম</h1>
                </div>
                <div class="user-info">
                    <span><i class="fas fa-user"></i> <?php echo $_SESSION['salesman_name']; ?></span>
                    <a href="dashboard.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                    </a>
                    <a href="logout.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-sign-out-alt"></i> লগআউট
                    </a>
                </div>
            </div>
        </div>
    </header>

    <nav class="nav-menu">
        <div class="container">
            <ul>
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="pos.php"><i class="fas fa-cash-register"></i> নতুন বিক্রয়</a></li>
                <li><a href="sales.php" class="active"><i class="fas fa-list"></i> বিক্রয় তালিকা</a></li>
                <li><a href="products.php"><i class="fas fa-box"></i> পণ্য তালিকা</a></li>
                <li><a href="reports.php"><i class="fas fa-chart-bar"></i> রিপোর্ট</a></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="page-header">
                <h2><i class="fas fa-list"></i> বিক্রয় তালিকা</h2>
                <p>আপনার সকল বিক্রয়ের তালিকা এবং পরিসংখ্যান</p>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-receipt" style="color: #667eea;"></i>
                    <h3><?php echo number_format($stats['total_count']); ?></h3>
                    <p>মোট বিক্রয়</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-money-bill-wave" style="color: #2ed573;"></i>
                    <h3>৳<?php echo number_format($stats['total_amount'], 2); ?></h3>
                    <p>মোট আয়</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-chart-line" style="color: #ffa502;"></i>
                    <h3>৳<?php echo number_format($stats['avg_amount'], 2); ?></h3>
                    <p>গড় বিক্রয়</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-calendar-day" style="color: #ff4757;"></i>
                    <h3><?php echo $totalSales; ?></h3>
                    <p>ফিল্টার করা বিক্রয়</p>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters">
                <form method="GET">
                    <div class="filters-grid">
                        <div class="form-group">
                            <label>শুরুর তারিখ</label>
                            <input type="date" name="date_from" class="form-control" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="form-group">
                            <label>শেষ তারিখ</label>
                            <input type="date" name="date_to" class="form-control" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="form-group">
                            <label>পেমেন্ট পদ্ধতি</label>
                            <select name="payment_method" class="form-control">
                                <option value="">সব পদ্ধতি</option>
                                <?php foreach ($payment_methods as $value => $label): ?>
                                    <option value="<?php echo $value; ?>" <?php echo $payment_method == $value ? 'selected' : ''; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>খুঁজুন</label>
                            <input type="text" name="search" class="form-control" placeholder="বিক্রয় নং, কাস্টমার..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> ফিল্টার করুন
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Sales Table -->
            <div class="sales-table">
                <?php if (empty($sales)): ?>
                    <div style="padding: 3rem; text-align: center; color: #666;">
                        <i class="fas fa-receipt fa-3x" style="margin-bottom: 1rem;"></i>
                        <h3>কোন বিক্রয় পাওয়া যায়নি</h3>
                        <p>নির্বাচিত ফিল্টার অনুযায়ী কোন বিক্রয় খুঁজে পাওয়া যায়নি।</p>
                        <a href="pos.php" class="btn btn-primary" style="margin-top: 1rem;">
                            <i class="fas fa-plus"></i> নতুন বিক্রয় করুন
                        </a>
                    </div>
                <?php else: ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>বিক্রয় নং</th>
                                <th>কাস্টমার</th>
                                <th>পণ্য</th>
                                <th>মোট</th>
                                <th>ছাড়</th>
                                <th>চূড়ান্ত</th>
                                <th>পেমেন্ট</th>
                                <th>তারিখ</th>
                                <th>অ্যাকশন</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($sales as $sale): ?>
                            <tr>
                                <td>
                                    <strong><?php echo $sale['sale_number']; ?></strong>
                                </td>
                                <td>
                                    <?php if ($sale['customer_name']): ?>
                                        <strong><?php echo htmlspecialchars($sale['customer_name']); ?></strong><br>
                                        <small><?php echo htmlspecialchars($sale['customer_phone']); ?></small>
                                    <?php else: ?>
                                        <span style="color: #666;">ওয়াক-ইন কাস্টমার</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?php echo $sale['item_count']; ?> টি পণ্য</small><br>
                                    <small style="color: #666;" title="<?php echo htmlspecialchars($sale['items_summary']); ?>">
                                        <?php echo strlen($sale['items_summary']) > 30 ? substr($sale['items_summary'], 0, 30) . '...' : $sale['items_summary']; ?>
                                    </small>
                                </td>
                                <td>৳<?php echo number_format($sale['total_amount'], 2); ?></td>
                                <td>
                                    <?php if ($sale['discount_amount'] > 0): ?>
                                        <span style="color: #dc3545;">-৳<?php echo number_format($sale['discount_amount'], 2); ?></span>
                                    <?php else: ?>
                                        <span style="color: #666;">-</span>
                                    <?php endif; ?>
                                </td>
                                <td><strong>৳<?php echo number_format($sale['final_amount'], 2); ?></strong></td>
                                <td>
                                    <span class="badge badge-success">
                                        <?php echo $payment_methods[$sale['payment_method']]; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php echo date('d/m/Y', strtotime($sale['created_at'])); ?><br>
                                    <small><?php echo date('H:i', strtotime($sale['created_at'])); ?></small>
                                </td>
                                <td>
                                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                        <a href="sale_details.php?id=<?php echo $sale['id']; ?>" class="btn btn-primary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                                            <i class="fas fa-eye"></i> দেখুন
                                        </a>
                                        <a href="print_memo.php?id=<?php echo $sale['id']; ?>" class="btn btn-success" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" target="_blank">
                                            <i class="fas fa-print"></i> মেমো প্রিন্ট
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>&payment_method=<?php echo $payment_method; ?>&search=<?php echo urlencode($search); ?>">
                                    <i class="fas fa-chevron-left"></i> পূর্ববর্তী
                                </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <?php if ($i == $page): ?>
                                    <span class="current"><?php echo $i; ?></span>
                                <?php else: ?>
                                    <a href="?page=<?php echo $i; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>&payment_method=<?php echo $payment_method; ?>&search=<?php echo urlencode($search); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>&payment_method=<?php echo $payment_method; ?>&search=<?php echo urlencode($search); ?>">
                                    পরবর্তী <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </main>
</body>
</html>
